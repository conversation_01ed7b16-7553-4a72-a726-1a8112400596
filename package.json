{"name": "tbc-monorepo", "version": "1.0.0", "description": "TBC Monorepo with <PERSON><PERSON> backend and <PERSON><PERSON> frontend", "scripts": {"install:all": "npm run install:backend && npm run install:frontend", "install:backend": "cd backend && composer install", "install:frontend": "cd frontend && npm install", "dev": "docker compose up", "dev:detached": "docker compose up -d", "dev:logs": "docker compose logs -f", "dev:stop": "docker compose down", "build": "docker compose build", "backend:dev": "cd backend && ./vendor/bin/sail up", "frontend:dev": "cd frontend && npm run dev", "backend:build": "cd backend && ./vendor/bin/sail artisan build", "frontend:build": "cd frontend && npm run build", "artisan": "docker compose exec backend php artisan", "reverb": "docker compose exec backend php artisan reverb:start", "migrate": "npm run artisan migrate", "migrate:fresh": "npm run artisan migrate:fresh", "migrate:status": "npm run artisan migrate:status", "db:seed": "npm run artisan db:seed", "make:migration": "npm run artisan make:migration", "make:model": "npm run artisan make:model", "make:controller": "npm run artisan make:controller", "make:seeder": "npm run artisan make:seeder", "tinker": "docker compose exec backend php artisan tinker", "test:backend": "docker compose exec backend php artisan test", "routes": "npm run artisan route:list", "cache:clear": "npm run artisan cache:clear", "config:clear": "npm run artisan config:clear", "view:clear": "npm run artisan view:clear", "route:clear": "npm run artisan route:clear", "clear:all": "npm run artisan optimize:clear", "bash:backend": "docker compose exec backend bash", "bash:frontend": "docker compose exec frontend sh", "bash:mysql": "docker compose exec mysql bash", "bash:redis": "docker compose exec redis sh", "mysql": "docker compose exec mysql mysql -u root -p${DB_PASSWORD}", "mysql:no-password": "docker compose exec mysql mysql -u root", "redis-cli": "docker compose exec redis redis-cli"}}