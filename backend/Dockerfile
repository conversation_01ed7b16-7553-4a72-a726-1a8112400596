FROM php:8.2-fpm

# Install dependencies including Node.js and npm
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip \
    gnupg

# Install Node.js 22.x (latest) instead of 18.x
RUN curl -fsSL https://deb.nodesource.com/setup_22.x | bash - \
    && apt-get install -y nodejs

# Clear cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

# Get latest Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

WORKDIR /var/www/html

# Copy existing application directory
COPY . .

# Install dependencies
RUN composer install

# Set permissions
RUN chown -R www-data:www-data /var/www/html/storage /var/www/html/bootstrap/cache

EXPOSE 80 8080

# Create a script to run both the web server and Reverb
# RUN echo '#!/bin/bash\n\
# php artisan serve --host=0.0.0.0 --port=80 & \n\
# php artisan reverb:start --host=0.0.0.0 --port=8080 \n\
# ' > /var/www/html/start.sh

# RUN chmod +x /var/www/html/start.sh

# CMD ["/var/www/html/start.sh"]

CMD php artisan serve --host=0.0.0.0 --port=80 & php artisan queue:work & php artisan reverb:start --debug

