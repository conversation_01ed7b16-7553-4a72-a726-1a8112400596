# TBC Monorepo

A monorepo containing a Laravel backend and Vue frontend.

## Project Structure

- `backend/` - Laravel backend application
- `frontend/` - Vue frontend application
- `docker-compose.yml` - Docker Compose configuration for local development

## Development Setup

### Prerequisites

- <PERSON><PERSON> and Docker Compose
- Node.js and npm
- Composer (for local PHP development)

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/your-repo-name.git
   cd your-repo-name
   ```

2. Install dependencies:
   ```
   npm run install:all
   ```

3. Start the development environment:
   ```
   npm run dev
   ```

4. Access the applications:
   - Frontend: http://localhost:3000
   - Backend: http://localhost:8000

## Available Scripts

- `npm run dev` - Start all containers
- `npm run build` - Build all containers
- `npm run frontend:dev` - Start frontend development server
- `npm run backend:dev` - Start backend development server
- `npm run frontend:build` - Build frontend for production
- `npm run backend:build` - Build backend for production
