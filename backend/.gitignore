# Dependencies
node_modules
vendor
.pnp
.pnp.js

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist
build
public/build
public/hot

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.log

# Editor directories and files
.idea
.vscode/*
!.vscode/extensions.json
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS files
.DS_Store
Thumbs.db

# Docker volumes
docker-volumes

# Testing
coverage
.phpunit.result.cache
test-results/
playwright-report/
