<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CoinflipGame extends Model
{
    use HasFactory;

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'creator_id',
        'joiner_id',
        'currency_id',
        'amount',
        'creator_side',
        'joiner_side',
        'result',
        'status',
        'winner_id',
        'creator_balance_before',
        'creator_balance_after',
        'joiner_balance_before',
        'joiner_balance_after',
        'house_profit',
        'winner_profit',
        'reference_id',
        'meta',
        'created_at',
        'updated_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'integer',
        'creator_balance_before' => 'integer',
        'creator_balance_after' => 'integer',
        'joiner_balance_before' => 'integer',
        'joiner_balance_after' => 'integer',
        'house_profit' => 'integer',
        'winner_profit' => 'integer',
        'meta' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the creator of the coinflip game.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    /**
     * Get the joiner of the coinflip game.
     */
    public function joiner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'joiner_id');
    }

    /**
     * Get the winner of the coinflip game.
     */
    public function winner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'winner_id');
    }

    /**
     * Get the currency for this coinflip game.
     */
    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class);
    }

    /**
     * Generate a unique reference ID for the game.
     */
    public static function generateReferenceId(): string
    {
        return 'coinflip_' . uniqid() . '_' . time();
    }

    /**
     * Check if the game is waiting for a joiner.
     */
    public function isWaiting(): bool
    {
        return $this->status === 'waiting';
    }

    /**
     * Check if the game is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if the game is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Get the opposite side of the given side.
     */
    public static function getOppositeSide(string $side): string
    {
        return $side === 'heads' ? 'tails' : 'heads';
    }

    /**
     * Check if a user can join this game.
     */
    public function canBeJoinedBy(User $user): bool
    {
        return $this->isWaiting() &&
               $this->creator_id !== $user->id &&
               $this->joiner_id === null;
    }
}
