<?php

namespace App\Services;

use App\Models\Currency;
use App\Models\Transaction;
use App\Models\User;
use App\Models\UserBalance;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AuditService
{
    private BalanceService $balanceService;

    public function __construct(BalanceService $balanceService)
    {
        $this->balanceService = $balanceService;
    }

    /**
     * Verify transaction chain for a user and currency.
     * Recomputes and validates each transaction's hash and previous_hash.
     */
    public function verifyTransactionChain(User $user, Currency $currency): array
    {
        $transactions = Transaction::where('user_id', $user->id)
            ->where('currency_id', $currency->id)
            ->orderBy('id')
            ->get();

        if ($transactions->isEmpty()) {
            return [
                'success' => true,
                'message' => 'No transactions found for verification',
                'transactions_verified' => 0,
            ];
        }

        $expectedPreviousHash = hash('sha256', "genesis_{$user->id}_{$currency->id}");

        foreach ($transactions as $index => $transaction) {
            // Verify previous hash
            if (!hash_equals($expectedPreviousHash, $transaction->previous_hash)) {
                return [
                    'success' => false,
                    'error' => 'Previous hash mismatch',
                    'transaction_id' => $transaction->id,
                    'transaction_index' => $index,
                    'expected_previous_hash' => $expectedPreviousHash,
                    'actual_previous_hash' => $transaction->previous_hash,
                ];
            }

            // Verify transaction hash
            $expectedHash = $this->generateTransactionHash(
                $transaction->user_id,
                $transaction->currency_id,
                $transaction->amount,
                $transaction->balance_before,
                $transaction->balance_after,
                $transaction->type,
                $transaction->reference_id,
                $transaction->meta,
                $transaction->created_at,
                $transaction->previous_hash
            );

            if (!hash_equals($expectedHash, $transaction->hash)) {
                return [
                    'success' => false,
                    'error' => 'Transaction hash mismatch',
                    'transaction_id' => $transaction->id,
                    'transaction_index' => $index,
                    'expected_hash' => $expectedHash,
                    'actual_hash' => $transaction->hash,
                ];
            }

            // Set expected previous hash for next transaction
            $expectedPreviousHash = $transaction->hash;
        }

        return [
            'success' => true,
            'message' => 'Transaction chain verified successfully',
            'transactions_verified' => $transactions->count(),
        ];
    }

    /**
     * Verify balance hash for a user and currency.
     * Auto-seeds zero balance and genesis transaction if missing.
     */
    public function verifyBalanceHash(User $user, Currency $currency): array
    {
        $balance = UserBalance::where('user_id', $user->id)
            ->where('currency_id', $currency->id)
            ->first();

        // If balance doesn't exist, create zero balance and genesis transaction
        if (!$balance) {
            return $this->seedZeroBalance($user, $currency);
        }

        // Get the latest transaction for this user and currency
        $latestTransaction = Transaction::where('user_id', $user->id)
            ->where('currency_id', $currency->id)
            ->orderBy('id', 'desc')
            ->first();

        if (!$latestTransaction) {
            return [
                'success' => false,
                'error' => 'Balance exists but no transactions found',
                'balance_id' => $balance->id,
                'balance_amount' => $balance->balance,
            ];
        }

        // Recompute balance hash
        $expectedHash = $this->generateBalanceHash(
            $user->id,
            $currency->id,
            $balance->balance,
            $latestTransaction->id,
            $latestTransaction->hash
        );

        if (!hash_equals($expectedHash, $balance->balance_hash)) {
            return [
                'success' => false,
                'error' => 'Balance hash mismatch',
                'balance_id' => $balance->id,
                'expected_hash' => $expectedHash,
                'actual_hash' => $balance->balance_hash,
                'latest_transaction_id' => $latestTransaction->id,
            ];
        }

        return [
            'success' => true,
            'message' => 'Balance hash verified successfully',
            'balance_id' => $balance->id,
            'balance_amount' => $balance->balance,
            'latest_transaction_id' => $latestTransaction->id,
        ];
    }

    /**
     * Recalculate balance by summing all transactions.
     * Compare to stored balance and return result with diff.
     */
    public function recalculateBalance(User $user, Currency $currency): array
    {
        $balance = UserBalance::where('user_id', $user->id)
            ->where('currency_id', $currency->id)
            ->first();

        $calculatedBalance = Transaction::where('user_id', $user->id)
            ->where('currency_id', $currency->id)
            ->sum('amount');

        if (!$balance) {
            return [
                'success' => false,
                'error' => 'No balance record found',
                'calculated_balance' => $calculatedBalance,
                'stored_balance' => null,
                'difference' => null,
            ];
        }

        $difference = $calculatedBalance - $balance->balance;

        return [
            'success' => $difference === 0,
            'message' => $difference === 0 ? 'Balance calculation verified' : 'Balance mismatch detected',
            'calculated_balance' => $calculatedBalance,
            'stored_balance' => $balance->balance,
            'difference' => $difference,
            'balance_id' => $balance->id,
        ];
    }

    /**
     * Run comprehensive verification for a user and currency.
     */
    public function comprehensiveVerification(User $user, Currency $currency): array
    {
        $results = [
            'user_id' => $user->id,
            'currency_code' => $currency->code,
            'timestamp' => now(),
        ];

        // 1. Verify transaction chain
        $chainResult = $this->verifyTransactionChain($user, $currency);
        $results['transaction_chain'] = $chainResult;

        // 2. Verify balance hash (with auto-seeding if needed)
        $balanceHashResult = $this->verifyBalanceHash($user, $currency);
        $results['balance_hash'] = $balanceHashResult;

        // 3. Recalculate balance
        $balanceCalculationResult = $this->recalculateBalance($user, $currency);
        $results['balance_calculation'] = $balanceCalculationResult;

        // Overall success
        $results['overall_success'] = $chainResult['success'] && 
                                     $balanceHashResult['success'] && 
                                     $balanceCalculationResult['success'];

        return $results;
    }

    /**
     * Seed zero balance and genesis transaction for a user and currency.
     */
    private function seedZeroBalance(User $user, Currency $currency): array
    {
        return DB::transaction(function () use ($user, $currency) {
            // Create genesis transaction
            $genesisTransaction = $this->createGenesisTransaction($user, $currency);

            // Create zero balance
            $balance = new UserBalance([
                'user_id' => $user->id,
                'currency_id' => $currency->id,
                'balance' => 0,
                'updated_at' => now(),
            ]);

            // Generate balance hash
            $balance->balance_hash = $this->generateBalanceHash(
                $user->id,
                $currency->id,
                0,
                $genesisTransaction->id,
                $genesisTransaction->hash
            );

            $balance->save();

            Log::info('Zero balance seeded', [
                'user_id' => $user->id,
                'currency_id' => $currency->id,
                'balance_id' => $balance->id,
                'genesis_transaction_id' => $genesisTransaction->id,
            ]);

            return [
                'success' => true,
                'message' => 'Zero balance and genesis transaction created',
                'balance_id' => $balance->id,
                'genesis_transaction_id' => $genesisTransaction->id,
                'seeded' => true,
            ];
        });
    }

    /**
     * Create a genesis transaction.
     */
    private function createGenesisTransaction(User $user, Currency $currency): Transaction
    {
        $previousHash = hash('sha256', "genesis_{$user->id}_{$currency->id}");
        $createdAt = now();

        $transaction = new Transaction([
            'user_id' => $user->id,
            'currency_id' => $currency->id,
            'amount' => 0,
            'balance_before' => 0,
            'balance_after' => 0,
            'type' => 'genesis',
            'reference_id' => null,
            'meta' => null,
            'previous_hash' => $previousHash,
            'created_at' => $createdAt,
        ]);

        // Generate transaction hash
        $transaction->hash = $this->generateTransactionHash(
            $user->id,
            $currency->id,
            0,
            0,
            0,
            'genesis',
            null,
            null,
            $createdAt,
            $previousHash
        );

        $transaction->save();

        return $transaction;
    }

    /**
     * Generate transaction hash (same logic as BalanceService).
     */
    private function generateTransactionHash(
        int $userId,
        int $currencyId,
        int $amount,
        int $balanceBefore,
        int $balanceAfter,
        string $type,
        ?string $referenceId,
        ?array $meta,
        $createdAt,
        string $previousHash
    ): string {
        $data = [
            'user_id' => $userId,
            'currency_id' => $currencyId,
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $balanceAfter,
            'type' => $type,
            'reference_id' => $referenceId,
            'meta' => $meta ? json_encode($meta) : null,
            'created_at' => $createdAt->format('Y-m-d H:i:s'),
            'previous_hash' => $previousHash,
        ];

        ksort($data);
        return hash('sha256', json_encode($data));
    }

    /**
     * Generate balance hash (same logic as BalanceService).
     */
    private function generateBalanceHash(
        int $userId,
        int $currencyId,
        int $balance,
        int $lastTxnId,
        string $lastTxnHash
    ): string {
        $data = [
            'user_id' => $userId,
            'currency_id' => $currencyId,
            'balance' => $balance,
            'last_txn_id' => $lastTxnId,
            'last_txn_hash' => $lastTxnHash,
        ];

        ksort($data);
        return hash('sha256', json_encode($data));
    }
}
