<?php

namespace App\Services;

use App\Models\CoinflipGame;
use App\Models\Currency;
use App\Models\User;
use App\Events\CoinflipGameCreated;
use App\Events\CoinflipGameJoined;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CoinflipGameService
{
    private BalanceService $balanceService;
    private float $houseEdge;

    public function __construct(BalanceService $balanceService)
    {
        $this->balanceService = $balanceService;
        $this->houseEdge = 9.0; // 9% house edge
    }

    /**
     * Get the house edge percentage.
     */
    public function getHouseEdge(): float
    {
        return $this->houseEdge;
    }

    /**
     * Calculate minimum bet to preserve house edge after rounding.
     * Formula: (1 / 10^decimals) / (house_edge / 100)
     */
    public function calculateMinimumBet(Currency $currency): float
    {
        $smallestUnit = 1 / (10 ** $currency->decimals);
        $houseEdgeFraction = $this->houseEdge / 100.0;

        return $smallestUnit / $houseEdgeFraction;
    }

    /**
     * Generate a random coin flip result.
     * Returns 'heads' or 'tails'
     */
    public function generateCoinFlip(): string
    {
        // Generate random number: 0 = heads, 1 = tails
        return mt_rand(0, 1) === 0 ? 'heads' : 'tails';
    }

    /**
     * Validate a bet for coinflip game.
     */
    public function validateBet(User $user, Currency $currency, float $betAmount, string $side): array
    {
        $errors = [];

        // Validate bet amount
        if ($betAmount <= 0) {
            $errors[] = 'Bet amount must be positive';
        }

        // Check minimum bet
        $minimumBet = $this->calculateMinimumBet($currency);
        if ($betAmount < $minimumBet) {
            $errors[] = "Minimum bet is {$currency->formatAmount($minimumBet)}";
        }

        // Check user balance
        $balance = $this->balanceService->getBalance($user, $currency);
        $betAmountMinor = (int) round($betAmount * (10 ** $currency->decimals));

        if ($balance < $betAmountMinor) {
            $errors[] = 'Insufficient balance';
        }

        // Validate side
        if (!in_array($side, ['heads', 'tails'])) {
            $errors[] = 'Invalid side selection';
        }

        return $errors;
    }

    /**
     * Create a new coinflip game.
     */
    public function createGame(User $creator, Currency $currency, float $betAmount, string $side): CoinflipGame
    {
        // Validate the bet
        $errors = $this->validateBet($creator, $currency, $betAmount, $side);
        if (!empty($errors)) {
            throw new \InvalidArgumentException(implode(', ', $errors));
        }

        return DB::transaction(function () use ($creator, $currency, $betAmount, $side) {
            // Convert bet amount to minor units
            $betAmountMinor = (int) round($betAmount * (10 ** $currency->decimals));

            // Get current balance before any transactions
            $balanceBefore = $this->balanceService->getBalance($creator, $currency);

            // Generate reference ID for this game
            $referenceId = CoinflipGame::generateReferenceId();

            // Debit the bet amount upfront
            $this->balanceService->debit(
                $creator,
                $currency,
                $betAmountMinor,
                'coinflip_bet',
                $referenceId,
                [
                    'game_type' => 'coinflip',
                    'bet_amount' => $betAmountMinor,
                    'side' => $side,
                    'action' => 'game_created',
                ]
            );

            // Get balance after debit
            $balanceAfter = $this->balanceService->getBalance($creator, $currency);

            // Create coinflip game record
            $game = CoinflipGame::create([
                'creator_id' => $creator->id,
                'currency_id' => $currency->id,
                'amount' => $betAmountMinor,
                'creator_side' => $side,
                'status' => 'waiting',
                'creator_balance_before' => $balanceBefore,
                'creator_balance_after' => $balanceAfter,
                'house_profit' => 0, // Will be calculated when game is resolved
                'reference_id' => $referenceId,
                'meta' => [
                    'house_edge' => $this->houseEdge,
                    'timestamp' => now()->timestamp,
                ],
                'created_at' => now(),
            ]);

            // Broadcast game creation event
            broadcast(new CoinflipGameCreated($game));

            return $game;
        });
    }

    /**
     * Join an existing coinflip game.
     */
    public function joinGame(CoinflipGame $game, User $joiner): CoinflipGame
    {
        if (!$game->canBeJoinedBy($joiner)) {
            throw new \InvalidArgumentException('Cannot join this game');
        }

        $currency = $game->currency;
        $betAmount = $currency->fromMinorUnits($game->amount);

        // Validate joiner can afford the bet
        $errors = $this->validateBet($joiner, $currency, $betAmount, 'heads'); // Side doesn't matter for validation
        if (!empty($errors)) {
            throw new \InvalidArgumentException(implode(', ', $errors));
        }

        return DB::transaction(function () use ($game, $joiner, $currency) {
            // Lock the game for update
            $game = CoinflipGame::where('id', $game->id)->lockForUpdate()->first();

            // Double-check game can still be joined
            if (!$game->canBeJoinedBy($joiner)) {
                throw new \InvalidArgumentException('Game is no longer available');
            }

            // Get joiner's balance before transaction
            $joinerBalanceBefore = $this->balanceService->getBalance($joiner, $currency);

            // Debit joiner's bet amount
            $this->balanceService->debit(
                $joiner,
                $currency,
                $game->amount,
                'coinflip_bet',
                $game->reference_id,
                [
                    'game_type' => 'coinflip',
                    'bet_amount' => $game->amount,
                    'side' => CoinflipGame::getOppositeSide($game->creator_side),
                    'action' => 'game_joined',
                ]
            );

            // Get joiner's balance after debit
            $joinerBalanceAfter = $this->balanceService->getBalance($joiner, $currency);

            // Update game with joiner info
            $game->update([
                'joiner_id' => $joiner->id,
                'joiner_side' => CoinflipGame::getOppositeSide($game->creator_side),
                'joiner_balance_before' => $joinerBalanceBefore,
                'joiner_balance_after' => $joinerBalanceAfter,
                'updated_at' => now(),
            ]);

            // Resolve the game immediately
            $this->resolveGame($game);

            // Reload game with fresh data
            $game->refresh();

            // Broadcast game joined/resolved event
            broadcast(new CoinflipGameJoined($game));

            return $game;
        });
    }

    /**
     * Resolve a coinflip game by determining winner and distributing winnings.
     */
    private function resolveGame(CoinflipGame $game): void
    {
        // Generate coin flip result
        $result = $this->generateCoinFlip();

        // Determine winner
        $winnerId = null;
        if ($game->creator_side === $result) {
            $winnerId = $game->creator_id;
        } elseif ($game->joiner_side === $result) {
            $winnerId = $game->joiner_id;
        }

        // Calculate payouts
        $totalPot = $game->amount * 2; // Both players' bets
        $houseProfit = (int) round($totalPot * ($this->houseEdge / 100.0));
        $winnerProfit = $totalPot - $houseProfit;

        // Update game with results
        $game->update([
            'result' => $result,
            'status' => 'completed',
            'winner_id' => $winnerId,
            'house_profit' => $houseProfit,
            'winner_profit' => $winnerProfit,
            'updated_at' => now(),
        ]);

        // Credit winner if there is one
        if ($winnerId) {
            $winner = User::find($winnerId);
            $this->balanceService->credit(
                $winner,
                $game->currency,
                $winnerProfit,
                'coinflip_win',
                $game->reference_id,
                [
                    'game_type' => 'coinflip',
                    'game_id' => $game->id,
                    'total_pot' => $totalPot,
                    'house_profit' => $houseProfit,
                    'winner_profit' => $winnerProfit,
                    'result' => $result,
                    'action' => 'game_won',
                ]
            );

            // Update winner's balance in game record
            if ($winnerId === $game->creator_id) {
                $game->update(['creator_balance_after' => $this->balanceService->getBalance($winner, $game->currency)]);
            } else {
                $game->update(['joiner_balance_after' => $this->balanceService->getBalance($winner, $game->currency)]);
            }
        }
    }

    /**
     * Get waiting games for lobby display.
     */
    public function getWaitingGames(User $user, ?Currency $currency = null): array
    {
        $query = CoinflipGame::with(['creator', 'currency'])
            ->where('status', 'waiting')
            ->where('creator_id', '!=', $user->id)
            ->orderBy('created_at', 'desc');

        if ($currency) {
            $query->where('currency_id', $currency->id);
        }

        return $query->get()->toArray();
    }

    /**
     * Get user's games (both created and joined).
     */
    public function getUserGames(User $user, ?Currency $currency = null): array
    {
        $query = CoinflipGame::with(['creator', 'joiner', 'winner', 'currency'])
            ->where(function ($q) use ($user) {
                $q->where('creator_id', $user->id)
                  ->orWhere('joiner_id', $user->id);
            })
            ->orderBy('created_at', 'desc');

        if ($currency) {
            $query->where('currency_id', $currency->id);
        }

        return $query->limit(20)->get()->toArray();
    }

    /**
     * Join a game with a bot (instant join and resolution).
     */
    public function joinWithBot(CoinflipGame $game): CoinflipGame
    {
        if (!$game->isWaiting()) {
            throw new \InvalidArgumentException('Game is not available for bot join');
        }

        return DB::transaction(function () use ($game) {
            // Lock the game for update
            $game = CoinflipGame::where('id', $game->id)->lockForUpdate()->first();

            // Double-check game can still be joined
            if (!$game->isWaiting()) {
                throw new \InvalidArgumentException('Game is no longer available');
            }

            // Update game with bot as joiner (no actual user, just the side)
            $game->update([
                'joiner_side' => CoinflipGame::getOppositeSide($game->creator_side),
                'updated_at' => now(),
            ]);

            // Resolve the game immediately
            $this->resolveGame($game);

            // Reload game with fresh data
            $game->refresh();

            // Broadcast game joined/resolved event
            broadcast(new CoinflipGameJoined($game));

            return $game;
        });
    }
}
