<?php

namespace App\Events;

use App\Models\CoinflipGame;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CoinflipGameCreated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public CoinflipGame $game;

    /**
     * Create a new event instance.
     */
    public function __construct(CoinflipGame $game)
    {
        $this->game = $game;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new Channel('coinflip.lobby'),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'game.created';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        $currency = $this->game->currency;
        $amount = $currency->fromMinorUnits($this->game->amount);
        
        return [
            'id' => $this->game->id,
            'creator' => [
                'id' => $this->game->creator->id,
                'username' => $this->game->creator->username,
            ],
            'amount' => $amount,
            'amount_formatted' => $currency->formatAmount($amount),
            'creator_side' => $this->game->creator_side,
            'status' => $this->game->status,
            'currency_code' => $currency->code,
            'currency_symbol' => $currency->symbol,
            'reference_id' => $this->game->reference_id,
            'created_at' => $this->game->created_at->toISOString(),
        ];
    }
}
