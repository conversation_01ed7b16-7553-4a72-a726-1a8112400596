<?php

namespace App\Events;

use App\Models\CoinflipGame;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CoinflipGameJoined implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public CoinflipGame $game;

    /**
     * Create a new event instance.
     */
    public function __construct(CoinflipGame $game)
    {
        $this->game = $game;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        $channels = [
            new Channel('coinflip.lobby'),
        ];

        // Also broadcast to creator's private channel
        if ($this->game->creator) {
            $channels[] = new PrivateChannel("user.{$this->game->creator->id}");
        }

        // And to joiner's private channel if it exists
        if ($this->game->joiner) {
            $channels[] = new PrivateChannel("user.{$this->game->joiner->id}");
        }

        return $channels;
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'game.joined';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        $currency = $this->game->currency;
        $amount = $currency->fromMinorUnits($this->game->amount);
        
        return [
            'id' => $this->game->id,
            'creator' => $this->game->creator ? [
                'id' => $this->game->creator->id,
                'username' => $this->game->creator->username,
            ] : null,
            'joiner' => $this->game->joiner ? [
                'id' => $this->game->joiner->id,
                'username' => $this->game->joiner->username,
            ] : null,
            'winner' => $this->game->winner ? [
                'id' => $this->game->winner->id,
                'username' => $this->game->winner->username,
            ] : null,
            'amount' => $amount,
            'amount_formatted' => $currency->formatAmount($amount),
            'creator_side' => $this->game->creator_side,
            'joiner_side' => $this->game->joiner_side,
            'result' => $this->game->result,
            'status' => $this->game->status,
            'currency_code' => $currency->code,
            'currency_symbol' => $currency->symbol,
            'reference_id' => $this->game->reference_id,
            'created_at' => $this->game->created_at->toISOString(),
            'updated_at' => $this->game->updated_at ? $this->game->updated_at->toISOString() : null,
        ];
    }
}
