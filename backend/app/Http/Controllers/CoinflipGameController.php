<?php

namespace App\Http\Controllers;

use App\Models\CoinflipGame;
use App\Models\Currency;
use App\Services\CoinflipGameService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CoinflipGameController extends Controller
{
    private CoinflipGameService $coinflipGameService;

    public function __construct(CoinflipGameService $coinflipGameService)
    {
        $this->coinflipGameService = $coinflipGameService;
    }

    /**
     * Create a new coinflip game.
     */
    public function create(Request $request): JsonResponse
    {
        // Validate request first - this will automatically return 422 on validation failure
        $validated = $request->validate([
            'bet' => 'required|numeric|gt:0',
            'side' => 'required|string|in:heads,tails',
            'currency_id' => 'sometimes|integer|exists:currencies,id',
        ]);

        try {
            $user = $request->user();

            // Get currency (default to first available if not specified)
            $currencyId = $validated['currency_id'] ?? Currency::first()?->id;
            if (!$currencyId) {
                return response()->json([
                    'success' => false,
                    'error' => 'No currencies available',
                ], 400);
            }
            $currency = Currency::findOrFail($currencyId);

            // Create the coinflip game
            $game = $this->coinflipGameService->createGame(
                $user,
                $currency,
                $validated['bet'],
                $validated['side']
            );

            // Load relationships for response
            $game->load(['creator', 'currency']);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $game->id,
                    'amount' => $currency->fromMinorUnits($game->amount),
                    'amount_formatted' => $currency->formatAmount($currency->fromMinorUnits($game->amount)),
                    'creator_side' => $game->creator_side,
                    'status' => $game->status,
                    'currency_code' => $currency->code,
                    'currency_symbol' => $currency->symbol,
                    'reference_id' => $game->reference_id,
                    'created_at' => $game->created_at->toISOString(),
                ],
            ]);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to create game',
            ], 500);
        }
    }

    /**
     * Join an existing coinflip game.
     */
    public function join(Request $request, CoinflipGame $game): JsonResponse
    {
        try {
            $user = $request->user();

            // Join the game
            $game = $this->coinflipGameService->joinGame($game, $user);

            // Load relationships for response
            $game->load(['creator', 'joiner', 'winner', 'currency']);

            return response()->json([
                'success' => true,
                'data' => $this->formatGameResponse($game),
            ]);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to join game',
            ], 500);
        }
    }

    /**
     * Join a game with a bot.
     */
    public function joinWithBot(Request $request, CoinflipGame $game): JsonResponse
    {
        try {
            $user = $request->user();

            // Verify user is the creator
            if ($game->creator_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'error' => 'Only the game creator can call a bot',
                ], 403);
            }

            // Join with bot
            $game = $this->coinflipGameService->joinWithBot($game);

            // Load relationships for response
            $game->load(['creator', 'joiner', 'winner', 'currency']);

            return response()->json([
                'success' => true,
                'data' => $this->formatGameResponse($game),
            ]);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to join with bot',
            ], 500);
        }
    }

    /**
     * Get waiting games for lobby.
     */
    public function lobby(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $currencyId = $request->get('currency_id');
            $currency = $currencyId ? Currency::findOrFail($currencyId) : null;

            $waitingGames = $this->coinflipGameService->getWaitingGames($user, $currency);

            return response()->json([
                'success' => true,
                'data' => array_map([$this, 'formatGameResponse'], $waitingGames),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch lobby games',
            ], 500);
        }
    }

    /**
     * Get user's games.
     */
    public function userGames(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $currencyId = $request->get('currency_id');
            $currency = $currencyId ? Currency::findOrFail($currencyId) : null;

            $userGames = $this->coinflipGameService->getUserGames($user, $currency);

            return response()->json([
                'success' => true,
                'data' => array_map([$this, 'formatGameResponse'], $userGames),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch user games',
            ], 500);
        }
    }

    /**
     * Get coinflip game configuration.
     */
    public function config(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'house_edge' => $this->coinflipGameService->getHouseEdge(),
                'sides' => ['heads', 'tails'],
            ],
        ]);
    }

    /**
     * Get minimum bet for a currency.
     */
    public function minimumBet(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'currency_id' => 'required|integer|exists:currencies,id',
            ]);

            $currency = Currency::findOrFail($request->currency_id);
            $minimumBet = $this->coinflipGameService->calculateMinimumBet($currency);

            return response()->json([
                'success' => true,
                'data' => [
                    'minimum_bet' => $minimumBet,
                    'minimum_bet_formatted' => $currency->formatAmount($minimumBet),
                    'currency_code' => $currency->code,
                    'currency_symbol' => $currency->symbol,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to calculate minimum bet',
            ], 500);
        }
    }

    /**
     * Format game data for API response.
     */
    private function formatGameResponse($game): array
    {
        if (is_array($game)) {
            // Handle array format from service methods
            $currency = $game['currency'] ?? null;

            // For arrays, we need to handle currency conversion differently
            if ($currency && is_array($currency)) {
                $decimals = $currency['decimals'] ?? 2;
                $amount = $game['amount'] / (10 ** $decimals);
                $amountFormatted = ($currency['symbol'] ?? '$') . number_format($amount, $decimals);
            } else {
                $amount = $game['amount'];
                $amountFormatted = '$' . number_format($amount, 2);
            }

            return [
                'id' => $game['id'],
                'creator' => $game['creator'] ?? null,
                'joiner' => $game['joiner'] ?? null,
                'winner' => $game['winner'] ?? null,
                'amount' => $amount,
                'amount_formatted' => $amountFormatted,
                'creator_side' => $game['creator_side'],
                'joiner_side' => $game['joiner_side'] ?? null,
                'result' => $game['result'] ?? null,
                'status' => $game['status'],
                'currency_code' => $currency['code'] ?? null,
                'currency_symbol' => $currency['symbol'] ?? null,
                'reference_id' => $game['reference_id'],
                'created_at' => $game['created_at'],
                'updated_at' => $game['updated_at'] ?? null,
            ];
        }

        // Handle Eloquent model format
        $currency = $game->currency;
        $amount = $currency->fromMinorUnits($game->amount);

        return [
            'id' => $game->id,
            'creator' => $game->creator ? [
                'id' => $game->creator->id,
                'username' => $game->creator->username,
            ] : null,
            'joiner' => $game->joiner ? [
                'id' => $game->joiner->id,
                'username' => $game->joiner->username,
            ] : null,
            'winner' => $game->winner ? [
                'id' => $game->winner->id,
                'username' => $game->winner->username,
            ] : null,
            'amount' => $amount,
            'amount_formatted' => $currency->formatAmount($amount),
            'creator_side' => $game->creator_side,
            'joiner_side' => $game->joiner_side,
            'result' => $game->result,
            'status' => $game->status,
            'currency_code' => $currency->code,
            'currency_symbol' => $currency->symbol,
            'reference_id' => $game->reference_id,
            'created_at' => $game->created_at->toISOString(),
            'updated_at' => $game->updated_at ? $game->updated_at->toISOString() : null,
        ];
    }
}
