<?php

namespace App\Http\Controllers;

use App\Models\Currency;
use Illuminate\Http\JsonResponse;

class CurrencyController extends Controller
{
    /**
     * Get all available currencies.
     */
    public function index(): JsonResponse
    {
        $currencies = Currency::all();

        return response()->json([
            'success' => true,
            'data' => $currencies->map(function ($currency) {
                return [
                    'id' => $currency->id,
                    'code' => $currency->code,
                    'symbol' => $currency->symbol,
                    'decimals' => $currency->decimals,
                ];
            }),
        ]);
    }

    /**
     * Get a specific currency.
     */
    public function show(Currency $currency): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'id' => $currency->id,
                'code' => $currency->code,
                'symbol' => $currency->symbol,
                'decimals' => $currency->decimals,
            ],
        ]);
    }
}
