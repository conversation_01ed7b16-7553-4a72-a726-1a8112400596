<?php

namespace App\Http\Controllers;

use App\Models\CoinflipGame;
use App\Models\Currency;
use App\Models\Transaction;
use App\Models\DiceGame;
use App\Services\BalanceService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class BalanceController extends Controller
{
    private BalanceService $balanceService;

    public function __construct(BalanceService $balanceService)
    {
        $this->balanceService = $balanceService;
    }

    /**
     * Get all balances for the authenticated user.
     */
    public function index(): JsonResponse
    {
        $user = Auth::user();
        $balances = $this->balanceService->getBalances($user);

        $formattedBalances = [];
        foreach ($balances as $currencyCode => $balance) {
            $currency = Currency::where('code', $currencyCode)->first();
            if ($currency) {
                $formattedBalances[] = [
                    'currency' => [
                        'id' => $currency->id,
                        'code' => $currency->code,
                        'symbol' => $currency->symbol,
                        'decimals' => $currency->decimals,
                    ],
                    'balance' => $balance,
                    'balance_formatted' => $currency->fromMinorUnits($balance),
                    'updated_at' => now(),
                ];
            }
        }

        return response()->json([
            'success' => true,
            'data' => $formattedBalances,
        ]);
    }

    /**
     * Get balance for a specific currency.
     */
    public function show(Currency $currency): JsonResponse
    {
        $user = Auth::user();
        $balance = $this->balanceService->getBalance($user, $currency);

        return response()->json([
            'success' => true,
            'data' => [
                'currency' => [
                    'id' => $currency->id,
                    'code' => $currency->code,
                    'symbol' => $currency->symbol,
                    'decimals' => $currency->decimals,
                ],
                'balance' => $balance,
                'balance_formatted' => $currency->fromMinorUnits($balance),
                'updated_at' => now(), // Since we don't have the balance record, use current time
            ],
        ]);
    }

    /**
     * Process a balance transaction.
     */
    public function processTransaction(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'currency_id' => 'required|exists:currencies,id',
            'amount' => 'required|numeric',
            'type' => 'required|string|max:50',
            'reference_id' => 'nullable|string|max:255',
            'meta' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = Auth::user();
        $currency = Currency::findOrFail($request->currency_id);

        // Convert amount to minor units
        $amountInMinorUnits = $currency->toMinorUnits($request->amount);

        try {
            // Determine if this is a credit or debit based on amount
            if ($amountInMinorUnits > 0) {
                $transaction = $this->balanceService->credit(
                    $user,
                    $currency,
                    $amountInMinorUnits,
                    $request->type,
                    $request->reference_id,
                    $request->meta
                );
            } else {
                $transaction = $this->balanceService->debit(
                    $user,
                    $currency,
                    abs($amountInMinorUnits),
                    $request->type,
                    $request->reference_id,
                    $request->meta
                );
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'transaction_id' => $transaction->id,
                    'amount' => $transaction->amount,
                    'amount_formatted' => $currency->fromMinorUnits($transaction->amount),
                    'balance_before' => $transaction->balance_before,
                    'balance_before_formatted' => $currency->fromMinorUnits($transaction->balance_before),
                    'balance_after' => $transaction->balance_after,
                    'balance_after_formatted' => $currency->fromMinorUnits($transaction->balance_after),
                    'type' => $transaction->type,
                    'reference_id' => $transaction->reference_id,
                    'created_at' => $transaction->created_at,
                ],
            ], 201);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Transaction processing failed',
            ], 500);
        }
    }

    /**
     * Get user balance for header display - includes all supported currencies.
     */
    public function userBalance(): JsonResponse
    {
        $user = Auth::user();

        // Get all supported currencies
        $currencies = Currency::whereIn('code', Currency::CODES)->get();

        $balances = [];
        foreach ($currencies as $currency) {
            $balance = $this->balanceService->getBalance($user, $currency);
            $balances[] = [
                'currency_code' => $currency->code,
                'balance' => $balance,
                'formatted' => $currency->fromMinorUnits($balance),
                'symbol' => $currency->symbol,
                'decimals' => $currency->decimals,
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $balances,
        ]);
    }

    /**
     * Get transaction history for a specific currency.
     */
    public function transactions(Currency $currency, Request $request): JsonResponse
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'limit' => 'nullable|integer|min:1|max:100',
            'offset' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $limit = $request->get('limit', 20);
        $offset = $request->get('offset', 0);

        $transactions = Transaction::where('user_id', $user->id)
            ->where('currency_id', $currency->id)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->offset($offset)
            ->get();

        $formattedTransactions = $transactions->map(function ($transaction) use ($currency) {
            return [
                'id' => $transaction->id,
                'amount' => $transaction->amount,
                'amount_formatted' => $currency->fromMinorUnits($transaction->amount),
                'balance_before' => $transaction->balance_before,
                'balance_before_formatted' => $currency->fromMinorUnits($transaction->balance_before),
                'balance_after' => $transaction->balance_after,
                'balance_after_formatted' => $currency->fromMinorUnits($transaction->balance_after),
                'type' => $transaction->type,
                'reference_id' => $transaction->reference_id,
                'meta' => $transaction->meta,
                'created_at' => $transaction->created_at,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $formattedTransactions,
            'pagination' => [
                'limit' => $limit,
                'offset' => $offset,
                'total' => Transaction::where('user_id', $user->id)->where('currency_id', $currency->id)->count(),
            ],
        ]);
    }

    /**
     * Get all transactions across all currencies with filtering.
     */
    public function allTransactions(Request $request): JsonResponse
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'limit' => 'nullable|integer|min:1|max:100',
            'offset' => 'nullable|integer|min:0',
            'date_range' => 'nullable|in:all,1h,1d,30d',
            'type_filter' => 'nullable|in:all,deposits,withdrawals,bets,wins',
            'game_filter' => 'nullable|string',
            'currency_filter' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $limit = $request->get('limit', 20);
        $offset = $request->get('offset', 0);
        $dateRange = $request->get('date_range', 'all');
        $typeFilter = $request->get('type_filter', 'all');
        $gameFilter = $request->get('game_filter');
        $currencyFilter = $request->get('currency_filter');

        // Build the query
        $query = Transaction::where('user_id', $user->id)
            ->with('currency')
            ->orderBy('created_at', 'desc');

        // Apply date range filter
        if ($dateRange !== 'all') {
            $dateFrom = match ($dateRange) {
                '1h' => now()->subHour(),
                '1d' => now()->subDay(),
                '30d' => now()->subDays(30),
                default => null,
            };

            if ($dateFrom) {
                $query->where('created_at', '>=', $dateFrom);
            }
        }

        // Apply type filter
        if ($typeFilter !== 'all') {
            switch ($typeFilter) {
                case 'deposits':
                    $query->where('type', 'deposit');
                    break;
                case 'withdrawals':
                    $query->where('type', 'withdrawal');
                    break;
                case 'bets':
                    $query->where('type', 'like', '%bet%');
                    break;
                case 'wins':
                    $query->where('type', 'like', '%win%');
                    break;
            }
        }

        // Apply game filter
        if ($gameFilter) {
            $query->where('type', 'like', "%{$gameFilter}%");
        }

        // Apply currency filter
        if ($currencyFilter) {
            $currency = Currency::where('code', $currencyFilter)->first();
            if ($currency) {
                $query->where('currency_id', $currency->id);
            }
        }

        $total = $query->count();
        $transactions = $query->limit($limit)->offset($offset)->get();

        $formattedTransactions = $transactions->map(function ($transaction) {
            return [
                'id' => $transaction->id,
                'amount' => $transaction->amount,
                'amount_formatted' => $transaction->currency->fromMinorUnits($transaction->amount),
                'balance_before' => $transaction->balance_before,
                'balance_before_formatted' => $transaction->currency->fromMinorUnits($transaction->balance_before),
                'balance_after' => $transaction->balance_after,
                'balance_after_formatted' => $transaction->currency->fromMinorUnits($transaction->balance_after),
                'type' => $transaction->type,
                'reference_id' => $transaction->reference_id,
                'meta' => $transaction->meta,
                'currency' => [
                    'id' => $transaction->currency->id,
                    'code' => $transaction->currency->code,
                    'symbol' => $transaction->currency->symbol,
                    'decimals' => $transaction->currency->decimals,
                ],
                'created_at' => $transaction->created_at,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $formattedTransactions,
            'pagination' => [
                'limit' => $limit,
                'offset' => $offset,
                'total' => $total,
            ],
        ]);
    }

    /**
     * Get betting summary by game.
     */
    public function gameBettingSummary(Request $request): JsonResponse
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'date_range' => 'nullable|in:all,1h,1d,30d',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $dateRange = $request->get('date_range', 'all');

        // Apply date range filter
        $dateFrom = null;
        if ($dateRange !== 'all') {
            $dateFrom = match ($dateRange) {
                '1h' => now()->subHour(),
                '1d' => now()->subDay(),
                '30d' => now()->subDays(30),
                default => null,
            };
        }

        // Get dice game summary
        $diceQuery = DiceGame::where('user_id', $user->id)
            ->with('currency');

        if ($dateFrom) {
            $diceQuery->where('created_at', '>=', $dateFrom);
        }

        $diceGames = $diceQuery->get();

        // Group by currency and calculate totals
        $diceSummary = [];
        foreach ($diceGames as $game) {
            $currencyCode = $game->currency->code;
            if (!isset($diceSummary[$currencyCode])) {
                $diceSummary[$currencyCode] = [
                    'total_bet' => 0,
                    'total_profit' => 0,
                    'games_count' => 0,
                    'wins_count' => 0,
                    'currency' => [
                        'code' => $game->currency->code,
                        'symbol' => $game->currency->symbol,
                        'decimals' => $game->currency->decimals,
                    ],
                ];
            }

            $diceSummary[$currencyCode]['total_bet'] += $game->bet_amount;
            $diceSummary[$currencyCode]['total_profit'] += $game->profit;
            $diceSummary[$currencyCode]['games_count']++;
            if ($game->win) {
                $diceSummary[$currencyCode]['wins_count']++;
            }
        }

        // Format the dice summary
        foreach ($diceSummary as $currencyCode => &$summary) {
            $currency = Currency::where('code', $currencyCode)->first();
            $summary['total_bet_formatted'] = $currency->fromMinorUnits($summary['total_bet']);
            $summary['total_profit_formatted'] = $currency->fromMinorUnits($summary['total_profit']);
            $summary['win_rate'] = $summary['games_count'] > 0
                ? round(($summary['wins_count'] / $summary['games_count']) * 100, 2)
                : 0;
        }

        // Get coinflip game summary
        $coinflipQuery = CoinflipGame::where('creator_id', $user->id)
            ->orWhere('joiner_id', $user->id)
            ->with('currency')
            ->where('status', 'completed');

        if ($dateFrom) {
            $coinflipQuery->where('created_at', '>=', $dateFrom);
        }

        $coinflipGames = $coinflipQuery->get();

        // Group by currency and calculate totals for coinflip
        $coinflipSummary = [];
        foreach ($coinflipGames as $game) {
            $currencyCode = $game->currency->code;
            if (!isset($coinflipSummary[$currencyCode])) {
                $coinflipSummary[$currencyCode] = [
                    'total_bet' => 0,
                    'total_profit' => 0,
                    'games_count' => 0,
                    'wins_count' => 0,
                    'currency' => [
                        'code' => $game->currency->code,
                        'symbol' => $game->currency->symbol,
                        'decimals' => $game->currency->decimals,
                    ],
                ];
            }

            // Calculate user's bet amount and profit for this game
            $userBetAmount = $game->amount; // Each player bets the same amount
            $userProfit = 0;

            if ($game->winner_id === $user->id) {
                // User won - they get 91% of total pot minus their bet
                $totalPot = $game->amount * 2;
                $winnerPayout = $totalPot - $game->house_profit;
                $userProfit = $winnerPayout - $userBetAmount;
                $coinflipSummary[$currencyCode]['wins_count']++;
            } else {
                // User lost - they lose their bet
                $userProfit = -$userBetAmount;
            }

            $coinflipSummary[$currencyCode]['total_bet'] += $userBetAmount;
            $coinflipSummary[$currencyCode]['total_profit'] += $userProfit;
            $coinflipSummary[$currencyCode]['games_count']++;
        }

        // Format the coinflip summary
        foreach ($coinflipSummary as $currencyCode => &$summary) {
            $currency = Currency::where('code', $currencyCode)->first();
            $summary['total_bet_formatted'] = $currency->fromMinorUnits($summary['total_bet']);
            $summary['total_profit_formatted'] = $currency->fromMinorUnits($summary['total_profit']);
            $summary['win_rate'] = $summary['games_count'] > 0
                ? round(($summary['wins_count'] / $summary['games_count']) * 100, 2)
                : 0;
        }

        $gameSummary = [
            'dice' => $diceSummary,
            'coinflip' => $coinflipSummary,
        ];

        return response()->json([
            'success' => true,
            'data' => $gameSummary,
        ]);
    }
}
