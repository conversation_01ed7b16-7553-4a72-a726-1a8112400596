<?php

namespace App\Console\Commands;

use App\Models\Currency;
use App\Models\User;
use App\Services\AuditService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class VerifyAllBalances extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'balance:verify-all {--chunk=100 : Number of users to process at once}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify transaction chains, balance hashes, and balance calculations for all users and currencies';

    private AuditService $auditService;

    public function __construct(AuditService $auditService)
    {
        parent::__construct();
        $this->auditService = $auditService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $chunkSize = (int) $this->option('chunk');

        $this->info('Starting comprehensive balance verification for all users and currencies...');

        $currencies = Currency::all();
        $totalUsers = User::count();

        if ($currencies->isEmpty()) {
            $this->error('No currencies found.');
            return 1;
        }

        if ($totalUsers === 0) {
            $this->error('No users found.');
            return 1;
        }

        $this->info("Found {$totalUsers} users and {$currencies->count()} currencies to verify.");
        $this->line('');

        $totalVerifications = $totalUsers * $currencies->count();
        $currentVerification = 0;
        $failedVerifications = [];
        $seededBalances = [];

        // Create progress bar
        $progressBar = $this->output->createProgressBar($totalVerifications);
        $progressBar->setFormat('verbose');

        // Process users in chunks
        User::chunk($chunkSize, function ($users) use ($currencies, &$currentVerification, &$failedVerifications, &$seededBalances, $progressBar) {
            foreach ($users as $user) {
                foreach ($currencies as $currency) {
                    $currentVerification++;

                    try {
                        $result = $this->auditService->comprehensiveVerification($user, $currency);

                        // Track seeded balances
                        if (isset($result['balance_hash']['seeded']) && $result['balance_hash']['seeded']) {
                            $seededBalances[] = [
                                'user_id' => $user->id,
                                'currency_code' => $currency->code,
                            ];
                        }

                        // Track failures
                        if (!$result['overall_success']) {
                            $failedVerifications[] = [
                                'user_id' => $user->id,
                                'user_name' => $user->name,
                                'currency_code' => $currency->code,
                                'errors' => $this->extractErrors($result),
                            ];

                            // Log detailed failure
                            Log::error('Balance verification failed', [
                                'user_id' => $user->id,
                                'currency_code' => $currency->code,
                                'result' => $result,
                            ]);
                        }

                    } catch (\Exception $e) {
                        $failedVerifications[] = [
                            'user_id' => $user->id,
                            'user_name' => $user->name,
                            'currency_code' => $currency->code,
                            'errors' => ['exception' => $e->getMessage()],
                        ];

                        Log::error('Balance verification exception', [
                            'user_id' => $user->id,
                            'currency_code' => $currency->code,
                            'exception' => $e->getMessage(),
                            'trace' => $e->getTraceAsString(),
                        ]);
                    }

                    $progressBar->advance();
                }
            }
        });

        $progressBar->finish();
        $this->line('');
        $this->line('');

        // Display results
        $this->displayResults($totalVerifications, $failedVerifications, $seededBalances);

        return empty($failedVerifications) ? 0 : 1;
    }

    /**
     * Extract error messages from verification result.
     */
    private function extractErrors(array $result): array
    {
        $errors = [];

        if (!$result['transaction_chain']['success']) {
            $errors['transaction_chain'] = $result['transaction_chain']['error'];
        }

        if (!$result['balance_hash']['success']) {
            $errors['balance_hash'] = $result['balance_hash']['error'];
        }

        if (!$result['balance_calculation']['success']) {
            $errors['balance_calculation'] = $result['balance_calculation']['message'];
        }

        return $errors;
    }

    /**
     * Display verification results.
     */
    private function displayResults(int $totalVerifications, array $failedVerifications, array $seededBalances): void
    {
        $successfulVerifications = $totalVerifications - count($failedVerifications);

        $this->info("Verification Summary:");
        $this->line("  Total verifications: {$totalVerifications}");
        $this->line("  Successful: {$successfulVerifications}");
        $this->line("  Failed: " . count($failedVerifications));
        $this->line("  Seeded balances: " . count($seededBalances));

        // Display seeded balances
        if (!empty($seededBalances)) {
            $this->line('');
            $this->info('🌱 Seeded zero balances:');
            foreach ($seededBalances as $seeded) {
                $this->line("  User {$seeded['user_id']} - {$seeded['currency_code']}");
            }
        }

        // Display failures
        if (!empty($failedVerifications)) {
            $this->line('');
            $this->error('❌ Failed verifications:');

            foreach ($failedVerifications as $failure) {
                $this->line('');
                $this->line("  User: {$failure['user_name']} (ID: {$failure['user_id']})");
                $this->line("  Currency: {$failure['currency_code']}");
                $this->line("  Errors:");

                foreach ($failure['errors'] as $type => $error) {
                    $this->line("    - {$type}: {$error}");
                }
            }

            $this->line('');
            $this->line('💡 Check the logs for detailed error information.');
        } else {
            $this->line('');
            $this->info('✅ All verifications passed successfully!');
        }
    }
}
