<?php

namespace App\Console\Commands;

use App\Models\Currency;
use App\Models\User;
use App\Services\AuditService;
use Illuminate\Console\Command;

class VerifyBalance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'balance:verify {user_id} {--currency= : Currency code to verify (optional)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify transaction chain, balance hash, and balance calculation for a specific user';

    private AuditService $auditService;

    public function __construct(AuditService $auditService)
    {
        parent::__construct();
        $this->auditService = $auditService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id');
        $currencyCode = $this->option('currency');

        // Find user
        $user = User::find($userId);
        if (!$user) {
            $this->error("User with ID {$userId} not found.");
            return 1;
        }

        $this->info("Verifying balances for user: {$user->name} (ID: {$user->id})");

        // Get currencies to verify
        $currencies = $currencyCode
            ? Currency::where('code', $currencyCode)->get()
            : Currency::all();

        if ($currencies->isEmpty()) {
            $this->error($currencyCode ? "Currency '{$currencyCode}' not found." : "No currencies found.");
            return 1;
        }

        $overallSuccess = true;
        $results = [];

        foreach ($currencies as $currency) {
            $this->line(""); // Empty line for readability
            $this->info("Verifying {$currency->code} for user {$user->id}...");

            $result = $this->auditService->comprehensiveVerification($user, $currency);
            $results[] = $result;

            // Display results
            $this->displayVerificationResult($result);

            if (!$result['overall_success']) {
                $overallSuccess = false;
            }
        }

        $this->line(""); // Empty line for readability

        if ($overallSuccess) {
            $this->info("✅ All verifications passed successfully!");
        } else {
            $this->error("❌ Some verifications failed. Check the details above.");
        }

        return $overallSuccess ? 0 : 1;
    }

    /**
     * Display verification result for a single currency.
     */
    private function displayVerificationResult(array $result): void
    {

        // Transaction Chain Verification
        $chainResult = $result['transaction_chain'];
        if ($chainResult['success']) {
            $this->line("  ✅ Transaction chain: {$chainResult['message']} ({$chainResult['transactions_verified']} transactions)");
        } else {
            $this->line("  ❌ Transaction chain: {$chainResult['error']}");
            if (isset($chainResult['transaction_id'])) {
                $this->line("     Failed at transaction ID: {$chainResult['transaction_id']}");
            }
        }

        // Balance Hash Verification
        $balanceHashResult = $result['balance_hash'];
        if ($balanceHashResult['success']) {
            $message = $balanceHashResult['message'];
            if (isset($balanceHashResult['seeded']) && $balanceHashResult['seeded']) {
                $this->line("  🌱 Balance hash: {$message}");
            } else {
                $this->line("  ✅ Balance hash: {$message}");
            }
        } else {
            $this->line("  ❌ Balance hash: {$balanceHashResult['error']}");
        }

        // Balance Calculation Verification
        $balanceCalcResult = $result['balance_calculation'];
        if ($balanceCalcResult['success']) {
            $this->line("  ✅ Balance calculation: {$balanceCalcResult['message']}");
            $this->line("     Balance: {$balanceCalcResult['stored_balance']}");
        } else {
            $this->line("  ❌ Balance calculation: {$balanceCalcResult['message']}");
            $this->line("     Stored: {$balanceCalcResult['stored_balance']}, Calculated: {$balanceCalcResult['calculated_balance']}, Diff: {$balanceCalcResult['difference']}");
        }
    }
}
