<?php

namespace Tests\Feature;

use App\Models\Currency;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CurrencyModelTest extends TestCase
{
    use RefreshDatabase;

    public function test_currency_creation(): void
    {
        $currency = Currency::create([
            'code' => 'USD',
            'decimals' => 2,
            'symbol' => '$',
        ]);

        $this->assertEquals('USD', $currency->code);
        $this->assertEquals(2, $currency->decimals);
        $this->assertEquals('$', $currency->symbol);
    }

    public function test_currency_code_uniqueness(): void
    {
        Currency::create([
            'code' => 'USD',
            'decimals' => 2,
            'symbol' => '$',
        ]);

        $this->expectException(\Illuminate\Database\QueryException::class);

        Currency::create([
            'code' => 'USD', // Duplicate code
            'decimals' => 2,
            'symbol' => '$',
        ]);
    }

    public function test_minor_to_major_units_conversion(): void
    {
        $usdCurrency = Currency::create([
            'code' => 'USD',
            'decimals' => 2,
            'symbol' => '$',
        ]);

        $pointsCurrency = Currency::create([
            'code' => 'POINTS',
            'decimals' => 0,
            'symbol' => 'PTS',
        ]);

        // Test USD (2 decimals)
        $this->assertEquals(100.50, $usdCurrency->fromMinorUnits(10050));
        $this->assertEquals(0.01, $usdCurrency->fromMinorUnits(1));

        // Test POINTS (0 decimals)
        $this->assertEquals(100.0, $pointsCurrency->fromMinorUnits(100));
        $this->assertEquals(1.0, $pointsCurrency->fromMinorUnits(1));
    }

    public function test_major_to_minor_units_conversion(): void
    {
        $usdCurrency = Currency::create([
            'code' => 'USD',
            'decimals' => 2,
            'symbol' => '$',
        ]);

        $usdtCurrency = Currency::create([
            'code' => 'USDT',
            'decimals' => 6,
            'symbol' => 'USDT',
        ]);

        // Test USD (2 decimals)
        $this->assertEquals(10050, $usdCurrency->toMinorUnits(100.50));
        $this->assertEquals(1, $usdCurrency->toMinorUnits(0.01));

        // Test USDT (6 decimals)
        $this->assertEquals(1000000, $usdtCurrency->toMinorUnits(1.0));
        $this->assertEquals(1500000, $usdtCurrency->toMinorUnits(1.5));
    }

    public function test_currency_constants(): void
    {
        $expectedCodes = ['USD', 'USDT', 'POINTS'];
        $this->assertEquals($expectedCodes, Currency::CODES);
    }

    public function test_currency_relationships(): void
    {
        $currency = Currency::create([
            'code' => 'USD',
            'decimals' => 2,
            'symbol' => '$',
        ]);

        // Test that relationships exist (even if empty)
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $currency->userBalances);
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $currency->transactions);
    }
}
