<?php

namespace Tests\Feature;

use App\Models\Currency;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TransactionModelTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Currency $currency;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->currency = Currency::create([
            'code' => 'USD',
            'decimals' => 2,
            'symbol' => '$',
        ]);
    }

    public function test_transaction_creation(): void
    {
        $genesisHash = hash('sha256', "genesis_{$this->user->id}_{$this->currency->id}");

        $transaction = Transaction::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 10000,
            'balance_before' => 0,
            'balance_after' => 10000,
            'type' => 'deposit',
            'reference_id' => 'test-ref',
            'meta' => ['source' => 'test'],
            'previous_hash' => $genesisHash,
            'hash' => 'temp-hash',
            'created_at' => now(),
        ]);

        $this->assertEquals($this->user->id, $transaction->user_id);
        $this->assertEquals($this->currency->id, $transaction->currency_id);
        $this->assertEquals(10000, $transaction->amount);
        $this->assertEquals('deposit', $transaction->type);
        $this->assertEquals(['source' => 'test'], $transaction->meta);
    }

    public function test_transaction_hash_generation(): void
    {
        $genesisHash = hash('sha256', "genesis_{$this->user->id}_{$this->currency->id}");
        $createdAt = now();

        $transaction = new Transaction([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 10000,
            'balance_before' => 0,
            'balance_after' => 10000,
            'type' => 'deposit',
            'reference_id' => 'test-ref',
            'meta' => ['source' => 'test'],
            'previous_hash' => $genesisHash,
            'created_at' => $createdAt,
        ]);

        // Generate hash manually using the same logic as BalanceService
        $data = [
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 10000,
            'balance_before' => 0,
            'balance_after' => 10000,
            'type' => 'deposit',
            'reference_id' => 'test-ref',
            'meta' => json_encode(['source' => 'test']),
            'created_at' => $createdAt->format('Y-m-d H:i:s'),
            'previous_hash' => $genesisHash,
        ];
        ksort($data);
        $expectedHash = hash('sha256', json_encode($data));

        $transaction->hash = $expectedHash;
        $transaction->save();

        $this->assertNotEmpty($transaction->hash);
        $this->assertEquals(64, strlen($transaction->hash)); // SHA-256 produces 64 character hex string
        $this->assertEquals($expectedHash, $transaction->hash);
    }

    public function test_transaction_chain_verification(): void
    {
        $genesisHash = hash('sha256', "genesis_{$this->user->id}_{$this->currency->id}");

        // Create first transaction
        $transaction1 = Transaction::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 10000,
            'balance_before' => 0,
            'balance_after' => 10000,
            'type' => 'deposit',
            'previous_hash' => $genesisHash,
            'hash' => 'temp-hash',
            'created_at' => now(),
        ]);
        // Generate hash for transaction1
        $data1 = [
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 10000,
            'balance_before' => 0,
            'balance_after' => 10000,
            'type' => 'deposit',
            'reference_id' => null,
            'meta' => null,
            'created_at' => $transaction1->created_at->format('Y-m-d H:i:s'),
            'previous_hash' => $genesisHash,
        ];
        ksort($data1);
        $transaction1->hash = hash('sha256', json_encode($data1));
        $transaction1->save();

        // Create second transaction
        $createdAt2 = now();
        $transaction2 = Transaction::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => -5000,
            'balance_before' => 10000,
            'balance_after' => 5000,
            'type' => 'withdrawal',
            'previous_hash' => $transaction1->hash,
            'hash' => 'temp-hash',
            'created_at' => $createdAt2,
        ]);

        // Generate hash for transaction2
        $data2 = [
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => -5000,
            'balance_before' => 10000,
            'balance_after' => 5000,
            'type' => 'withdrawal',
            'reference_id' => null,
            'meta' => null,
            'created_at' => $createdAt2->format('Y-m-d H:i:s'),
            'previous_hash' => $transaction1->hash,
        ];
        ksort($data2);
        $transaction2->hash = hash('sha256', json_encode($data2));
        $transaction2->save();

        // Verify chain integrity
        $this->assertTrue($transaction1->verifyChain());
        $this->assertTrue($transaction2->verifyChain());
    }

    public function test_transaction_chain_verification_fails_with_wrong_previous_hash(): void
    {
        $genesisHash = hash('sha256', "genesis_{$this->user->id}_{$this->currency->id}");

        // Create first transaction
        $transaction1 = Transaction::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 10000,
            'balance_before' => 0,
            'balance_after' => 10000,
            'type' => 'deposit',
            'previous_hash' => $genesisHash,
            'hash' => 'temp-hash',
            'created_at' => now(),
        ]);
        // Generate hash for transaction1
        $data1 = [
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 10000,
            'balance_before' => 0,
            'balance_after' => 10000,
            'type' => 'deposit',
            'reference_id' => null,
            'meta' => null,
            'created_at' => $transaction1->created_at->format('Y-m-d H:i:s'),
            'previous_hash' => $genesisHash,
        ];
        ksort($data1);
        $transaction1->hash = hash('sha256', json_encode($data1));
        $transaction1->save();

        // Create second transaction with wrong previous hash
        $createdAt2 = now();
        $transaction2 = Transaction::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => -5000,
            'balance_before' => 10000,
            'balance_after' => 5000,
            'type' => 'withdrawal',
            'previous_hash' => 'wrong-hash',
            'hash' => 'temp-hash',
            'created_at' => $createdAt2,
        ]);

        // Generate hash for transaction2
        $data2 = [
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => -5000,
            'balance_before' => 10000,
            'balance_after' => 5000,
            'type' => 'withdrawal',
            'reference_id' => null,
            'meta' => null,
            'created_at' => $createdAt2->format('Y-m-d H:i:s'),
            'previous_hash' => 'wrong-hash',
        ];
        ksort($data2);
        $transaction2->hash = hash('sha256', json_encode($data2));
        $transaction2->save();

        // Chain verification should fail
        $this->assertTrue($transaction1->verifyChain());
        $this->assertFalse($transaction2->verifyChain());
    }

    public function test_get_previous_transaction(): void
    {
        $genesisHash = hash('sha256', "genesis_{$this->user->id}_{$this->currency->id}");

        // Create first transaction
        $transaction1 = Transaction::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 10000,
            'balance_before' => 0,
            'balance_after' => 10000,
            'type' => 'deposit',
            'previous_hash' => $genesisHash,
            'hash' => 'temp-hash',
            'created_at' => now(),
        ]);

        // Create second transaction
        $transaction2 = Transaction::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => -5000,
            'balance_before' => 10000,
            'balance_after' => 5000,
            'type' => 'withdrawal',
            'previous_hash' => $transaction1->hash,
            'hash' => 'temp-hash',
            'created_at' => now(),
        ]);

        // Test getting previous transaction
        $this->assertNull($transaction1->getPreviousTransaction());
        $this->assertEquals($transaction1->id, $transaction2->getPreviousTransaction()->id);
    }

    public function test_transaction_relationships(): void
    {
        $genesisHash = hash('sha256', "genesis_{$this->user->id}_{$this->currency->id}");

        $transaction = Transaction::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 10000,
            'balance_before' => 0,
            'balance_after' => 10000,
            'type' => 'deposit',
            'previous_hash' => $genesisHash,
            'hash' => 'temp-hash',
            'created_at' => now(),
        ]);

        // Test relationships
        $this->assertEquals($this->user->id, $transaction->user->id);
        $this->assertEquals($this->currency->id, $transaction->currency->id);
    }
}
