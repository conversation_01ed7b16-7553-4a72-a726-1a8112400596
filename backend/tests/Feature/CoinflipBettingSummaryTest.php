<?php

namespace Tests\Feature;

use App\Models\CoinflipGame;
use App\Models\Currency;
use App\Models\User;
use App\Services\BalanceService;
use App\Services\CoinflipGameService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class CoinflipBettingSummaryTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Currency $currency;
    protected BalanceService $balanceService;
    protected CoinflipGameService $coinflipGameService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->currency = Currency::create([
            'code' => 'USD',
            'decimals' => 2,
            'symbol' => '$',
        ]);

        $this->balanceService = new BalanceService();
        $this->coinflipGameService = new CoinflipGameService($this->balanceService);
        
        $this->balanceService->startBalance<PERSON>hain($this->user);
        $this->balanceService->credit($this->user, $this->currency, 10000, 'test_deposit'); // $100.00
    }

    public function test_betting_summary_includes_coinflip_games(): void
    {
        Sanctum::actingAs($this->user);

        // Create and resolve a coinflip game
        $game = $this->coinflipGameService->createGame($this->user, $this->currency, 10.0, 'heads');
        $this->coinflipGameService->joinWithBot($game);

        // Get betting summary
        $response = $this->getJson('/api/game-betting-summary');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonStructure([
                'success',
                'data' => [
                    'coinflip' => [
                        'USD' => [
                            'total_bet',
                            'total_bet_formatted',
                            'total_profit',
                            'total_profit_formatted',
                            'games_count',
                            'wins_count',
                            'win_rate',
                            'currency' => [
                                'code',
                                'symbol',
                                'decimals',
                            ],
                        ],
                    ],
                ],
            ]);

        $data = $response->json('data');
        
        // Verify coinflip data exists
        $this->assertArrayHasKey('coinflip', $data);
        $this->assertArrayHasKey('USD', $data['coinflip']);
        
        $coinflipData = $data['coinflip']['USD'];
        $this->assertEquals(1, $coinflipData['games_count']);
        $this->assertEquals(10.0, $coinflipData['total_bet_formatted']);
        $this->assertIsNumeric($coinflipData['total_profit_formatted']);
        $this->assertIsNumeric($coinflipData['win_rate']);
    }

    public function test_betting_summary_with_multiple_coinflip_games(): void
    {
        Sanctum::actingAs($this->user);

        // Create multiple coinflip games
        for ($i = 0; $i < 3; $i++) {
            $game = $this->coinflipGameService->createGame($this->user, $this->currency, 5.0, 'heads');
            $this->coinflipGameService->joinWithBot($game);
        }

        // Get betting summary
        $response = $this->getJson('/api/game-betting-summary');

        $response->assertStatus(200);
        
        $data = $response->json('data');
        $coinflipData = $data['coinflip']['USD'];
        
        $this->assertEquals(3, $coinflipData['games_count']);
        $this->assertEquals(15.0, $coinflipData['total_bet_formatted']); // 3 games * $5 each
    }

    public function test_betting_summary_with_date_range_filter(): void
    {
        Sanctum::actingAs($this->user);

        // Create a coinflip game
        $game = $this->coinflipGameService->createGame($this->user, $this->currency, 10.0, 'heads');
        $this->coinflipGameService->joinWithBot($game);

        // Get betting summary with 1 day filter
        $response = $this->getJson('/api/game-betting-summary?date_range=1d');

        $response->assertStatus(200);
        
        $data = $response->json('data');
        $this->assertArrayHasKey('coinflip', $data);
        $this->assertArrayHasKey('USD', $data['coinflip']);
        
        $coinflipData = $data['coinflip']['USD'];
        $this->assertEquals(1, $coinflipData['games_count']);
    }
}
