<?php

namespace Tests\Feature;

use App\Models\Currency;
use App\Models\User;
use App\Services\BalanceService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BalanceVerificationCommandsTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Currency $currency;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create(['name' => 'Test User']);
        $this->currency = Currency::create([
            'code' => 'USD',
            'decimals' => 2,
            'symbol' => '$',
        ]);
    }

    public function test_balance_verify_command_with_valid_user(): void
    {
        // Create some transactions
        $balanceService = new BalanceService();
        $balanceService->credit($this->user, $this->currency, 1000, 'deposit');

        $this->artisan('balance:verify', ['user_id' => $this->user->id])
            ->expectsOutput('Verifying balances for user: Test User (ID: ' . $this->user->id . ')')
            ->expectsOutput('Verifying USD for user ' . $this->user->id . '...')
            ->expectsOutput('✅ All verifications passed successfully!')
            ->assertExitCode(0);
    }

    public function test_balance_verify_command_with_specific_currency(): void
    {
        // Create another currency
        $pointsCurrency = Currency::create([
            'code' => 'POINTS',
            'decimals' => 0,
            'symbol' => 'PTS',
        ]);

        // Create transactions for both currencies
        $balanceService = new BalanceService();
        $balanceService->credit($this->user, $this->currency, 1000, 'deposit');
        $balanceService->credit($this->user, $pointsCurrency, 500, 'reward');

        $this->artisan('balance:verify', [
                'user_id' => $this->user->id,
                '--currency' => 'USD'
            ])
            ->expectsOutput('Verifying USD for user ' . $this->user->id . '...')
            ->doesntExpectOutput('Verifying POINTS for user ' . $this->user->id . '...')
            ->assertExitCode(0);
    }

    public function test_balance_verify_command_with_invalid_user(): void
    {
        $this->artisan('balance:verify', ['user_id' => 99999])
            ->expectsOutput('User with ID 99999 not found.')
            ->assertExitCode(1);
    }

    public function test_balance_verify_command_with_invalid_currency(): void
    {
        $this->artisan('balance:verify', [
                'user_id' => $this->user->id,
                '--currency' => 'INVALID'
            ])
            ->expectsOutput("Currency 'INVALID' not found.")
            ->assertExitCode(1);
    }

    public function test_balance_verify_command_with_seeding(): void
    {
        // User with no transactions should trigger seeding
        $this->artisan('balance:verify', ['user_id' => $this->user->id])
            ->expectsOutput('  🌱 Balance hash: Zero balance and genesis transaction created')
            ->assertExitCode(0);

        // Verify that balance and genesis transaction were created
        $this->assertDatabaseHas('user_balances', [
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'balance' => 0,
        ]);

        $this->assertDatabaseHas('transactions', [
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'type' => 'genesis',
            'amount' => 0,
        ]);
    }

    public function test_balance_verify_command_with_corrupted_data(): void
    {
        // Create a transaction and corrupt it
        $balanceService = new BalanceService();
        $transaction = $balanceService->credit($this->user, $this->currency, 1000, 'deposit');

        // Corrupt the transaction hash
        $transaction->hash = 'corrupted_hash';
        $transaction->save();

        $this->artisan('balance:verify', ['user_id' => $this->user->id])
            ->expectsOutput('  ❌ Transaction chain: Transaction hash mismatch')
            ->expectsOutput('❌ Some verifications failed. Check the details above.')
            ->assertExitCode(1);
    }

    public function test_balance_verify_all_command(): void
    {
        // Create multiple users and currencies
        $user2 = User::factory()->create(['name' => 'User 2']);
        $pointsCurrency = Currency::create([
            'code' => 'POINTS',
            'decimals' => 0,
            'symbol' => 'PTS',
        ]);

        // Create some transactions
        $balanceService = new BalanceService();
        $balanceService->credit($this->user, $this->currency, 1000, 'deposit');
        $balanceService->credit($user2, $pointsCurrency, 500, 'reward');

        $this->artisan('balance:verify-all')
            ->expectsOutput('Starting comprehensive balance verification for all users and currencies...')
            ->expectsOutput('✅ All verifications passed successfully!')
            ->assertExitCode(0);
    }

    public function test_balance_verify_all_command_with_seeding(): void
    {
        // Users with no transactions should trigger seeding
        User::factory()->create(['name' => 'User 2']);

        $this->artisan('balance:verify-all')
            ->expectsOutput('🌱 Seeded zero balances:')
            ->expectsOutputToContain('- USD')
            ->assertExitCode(0);
    }

    public function test_balance_verify_all_command_with_failures(): void
    {
        // Create a transaction and corrupt it
        $balanceService = new BalanceService();
        $transaction = $balanceService->credit($this->user, $this->currency, 1000, 'deposit');

        // Corrupt the transaction hash
        $transaction->hash = 'corrupted_hash';
        $transaction->save();

        $this->artisan('balance:verify-all')
            ->expectsOutput('❌ Failed verifications:')
            ->assertExitCode(1);
    }

    public function test_balance_verify_all_command_with_chunk_option(): void
    {
        $this->artisan('balance:verify-all', ['--chunk' => 50])
            ->expectsOutput('Starting comprehensive balance verification for all users and currencies...')
            ->assertExitCode(0);
    }

    public function test_commands_are_idempotent(): void
    {
        // Run verify command twice - should be safe
        $this->artisan('balance:verify', ['user_id' => $this->user->id])
            ->assertExitCode(0);

        $this->artisan('balance:verify', ['user_id' => $this->user->id])
            ->assertExitCode(0);

        // Run verify-all twice - should be safe
        $this->artisan('balance:verify-all')
            ->assertExitCode(0);

        $this->artisan('balance:verify-all')
            ->assertExitCode(0);
    }

    public function test_commands_do_not_modify_existing_transactions(): void
    {
        // Create a transaction
        $balanceService = new BalanceService();
        $transaction = $balanceService->credit($this->user, $this->currency, 1000, 'deposit');

        $originalHash = $transaction->hash;
        $originalAmount = $transaction->amount;
        $originalCreatedAt = $transaction->created_at;

        // Run verification
        $this->artisan('balance:verify', ['user_id' => $this->user->id])
            ->assertExitCode(0);

        // Verify transaction wasn't modified
        $transaction->refresh();
        $this->assertEquals($originalHash, $transaction->hash);
        $this->assertEquals($originalAmount, $transaction->amount);
        $this->assertEquals($originalCreatedAt->format('Y-m-d H:i:s'), $transaction->created_at->format('Y-m-d H:i:s'));
    }
}
