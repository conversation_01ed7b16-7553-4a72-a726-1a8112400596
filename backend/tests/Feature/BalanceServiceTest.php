<?php

namespace Tests\Feature;

use App\Models\Currency;
use App\Models\User;
use App\Services\BalanceService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BalanceServiceTest extends TestCase
{
    use RefreshDatabase;

    private BalanceService $balanceService;
    private User $user;
    private Currency $usdCurrency;
    private Currency $pointsCurrency;

    protected function setUp(): void
    {
        parent::setUp();

        $this->balanceService = new BalanceService();

        // Create test user
        $this->user = User::factory()->create();

        // Create test currencies
        $this->usdCurrency = Currency::create([
            'code' => 'USD',
            'decimals' => 2,
            'symbol' => '$',
        ]);

        $this->pointsCurrency = Currency::create([
            'code' => 'POINTS',
            'decimals' => 0,
            'symbol' => 'PTS',
        ]);
    }

    public function test_initial_balance_creation(): void
    {
        $balance = $this->balanceService->getBalance($this->user, $this->usdCurrency);

        $this->assertEquals(0, $balance);
    }

    public function test_positive_transaction(): void
    {
        $amount = 10000; // $100.00 in cents

        $transaction = $this->balanceService->credit(
            $this->user,
            $this->usdCurrency,
            $amount,
            'deposit',
            'test-ref-123'
        );

        $this->assertEquals($amount, $transaction->amount);
        $this->assertEquals(0, $transaction->balance_before);
        $this->assertEquals($amount, $transaction->balance_after);
        $this->assertEquals('deposit', $transaction->type);
        $this->assertEquals('test-ref-123', $transaction->reference_id);

        // Verify hash integrity
        $this->assertTrue($this->balanceService->verifyTransactionIntegrity($transaction));

        // Verify balance is updated
        $balance = $this->balanceService->getBalance($this->user, $this->usdCurrency);
        $this->assertEquals($amount, $balance);
    }

    public function test_negative_transaction(): void
    {
        // First add some balance
        $this->balanceService->credit($this->user, $this->usdCurrency, 10000, 'deposit');

        $withdrawAmount = 5000; // $50.00

        $transaction = $this->balanceService->debit(
            $this->user,
            $this->usdCurrency,
            $withdrawAmount,
            'withdrawal'
        );

        $this->assertEquals(-$withdrawAmount, $transaction->amount);
        $this->assertEquals(10000, $transaction->balance_before);
        $this->assertEquals(5000, $transaction->balance_after);

        // Verify balance is updated
        $balance = $this->balanceService->getBalance($this->user, $this->usdCurrency);
        $this->assertEquals(5000, $balance);
    }

    public function test_insufficient_balance_throws_exception(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Insufficient balance');

        $this->balanceService->debit(
            $this->user,
            $this->usdCurrency,
            1000, // Try to withdraw from zero balance
            'withdrawal'
        );
    }

    public function test_transaction_chain_integrity(): void
    {
        // Create multiple transactions
        $transactions = [];
        $transactions[] = $this->balanceService->credit($this->user, $this->usdCurrency, 1000, 'deposit');
        $transactions[] = $this->balanceService->credit($this->user, $this->usdCurrency, 500, 'deposit');
        $transactions[] = $this->balanceService->debit($this->user, $this->usdCurrency, 200, 'withdrawal');

        // Verify each transaction in the chain
        foreach ($transactions as $transaction) {
            $this->assertTrue($this->balanceService->verifyTransactionIntegrity($transaction));
        }

        // Verify the entire chain
        $this->assertTrue($this->balanceService->verifyUserTransactionChain($this->user, $this->usdCurrency));
    }

    public function test_concurrent_transactions_are_atomic(): void
    {
        // This test simulates concurrent access by creating multiple transactions rapidly
        $initialAmount = 10000;
        $this->balanceService->credit($this->user, $this->usdCurrency, $initialAmount, 'deposit');

        $transactions = [];
        for ($i = 0; $i < 5; $i++) {
            $transactions[] = $this->balanceService->debit(
                $this->user,
                $this->usdCurrency,
                1000,
                'withdrawal',
                "concurrent-{$i}"
            );
        }

        // Final balance should be correct
        $balance = $this->balanceService->getBalance($this->user, $this->usdCurrency);
        $this->assertEquals(5000, $balance);

        // All transactions should be valid
        foreach ($transactions as $transaction) {
            $this->assertTrue($this->balanceService->verifyTransactionIntegrity($transaction));
        }
    }

    public function test_multiple_currencies_per_user(): void
    {
        // Add balance to both currencies
        $this->balanceService->credit($this->user, $this->usdCurrency, 10000, 'deposit');
        $this->balanceService->credit($this->user, $this->pointsCurrency, 500, 'reward');

        // Verify both balances exist and are correct
        $usdBalance = $this->balanceService->getBalance($this->user, $this->usdCurrency);
        $pointsBalance = $this->balanceService->getBalance($this->user, $this->pointsCurrency);

        $this->assertEquals(10000, $usdBalance);
        $this->assertEquals(500, $pointsBalance);

        // Verify both transaction chains
        $this->assertTrue($this->balanceService->verifyUserTransactionChain($this->user, $this->usdCurrency));
        $this->assertTrue($this->balanceService->verifyUserTransactionChain($this->user, $this->pointsCurrency));
    }
}
