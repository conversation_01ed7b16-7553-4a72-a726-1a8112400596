<?php

namespace Tests\Feature;

use App\Models\CoinflipGame;
use App\Models\Currency;
use App\Models\User;
use App\Services\BalanceService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class CoinflipGameApiTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected User $user2;
    protected Currency $currency;
    protected BalanceService $balanceService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->user2 = User::factory()->create();
        $this->currency = Currency::create([
            'code' => 'USD',
            'decimals' => 2,
            'symbol' => '$',
        ]);

        $this->balanceService = new BalanceService();
        $this->balanceService->startBalanceChain($this->user);
        $this->balanceService->startBalanceChain($this->user2);
        $this->balanceService->credit($this->user, $this->currency, 10000, 'test_deposit'); // $100.00
        $this->balanceService->credit($this->user2, $this->currency, 10000, 'test_deposit'); // $100.00
    }

    public function test_create_game_requires_authentication(): void
    {
        $response = $this->postJson('/api/coinflip/create', [
            'bet' => 10.0,
            'side' => 'heads',
        ]);

        $response->assertStatus(401);
    }

    public function test_create_game_with_valid_data(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/coinflip/create', [
            'bet' => 10.0,
            'side' => 'heads',
            'currency_id' => $this->currency->id,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'amount',
                    'amount_formatted',
                    'creator_side',
                    'status',
                    'currency_code',
                    'currency_symbol',
                    'reference_id',
                    'created_at',
                ],
            ]);

        $data = $response->json('data');
        $this->assertEquals('heads', $data['creator_side']);
        $this->assertEquals('waiting', $data['status']);
        $this->assertEquals(10.0, $data['amount']);
    }

    public function test_create_game_validates_required_fields(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/coinflip/create', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['bet', 'side']);
    }

    public function test_create_game_validates_side_values(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/coinflip/create', [
            'bet' => 10.0,
            'side' => 'invalid',
            'currency_id' => $this->currency->id,
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['side']);
    }

    public function test_create_game_validates_insufficient_balance(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/coinflip/create', [
            'bet' => 200.0, // More than $100 balance
            'side' => 'heads',
            'currency_id' => $this->currency->id,
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
            ]);
    }

    public function test_create_game_creates_database_record(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/coinflip/create', [
            'bet' => 10.0,
            'side' => 'heads',
            'currency_id' => $this->currency->id,
        ]);

        $response->assertStatus(200);

        $data = $response->json('data');
        $this->assertDatabaseHas('coinflip_games', [
            'id' => $data['id'],
            'creator_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 1000, // $10.00 in cents
            'creator_side' => 'heads',
            'status' => 'waiting',
            'reference_id' => $data['reference_id'],
        ]);
    }

    public function test_join_game_requires_authentication(): void
    {
        $game = CoinflipGame::factory()->create([
            'creator_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 1000,
            'creator_side' => 'heads',
            'status' => 'waiting',
        ]);

        $response = $this->postJson("/api/coinflip/{$game->id}/join");

        $response->assertStatus(401);
    }

    public function test_join_game_successfully(): void
    {
        // Create a game with user1
        Sanctum::actingAs($this->user);
        $createResponse = $this->postJson('/api/coinflip/create', [
            'bet' => 10.0,
            'side' => 'heads',
            'currency_id' => $this->currency->id,
        ]);

        $gameId = $createResponse->json('data.id');

        // Join with user2
        Sanctum::actingAs($this->user2);
        $response = $this->postJson("/api/coinflip/{$gameId}/join");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'creator',
                    'joiner',
                    'winner',
                    'amount',
                    'amount_formatted',
                    'creator_side',
                    'joiner_side',
                    'result',
                    'status',
                    'currency_code',
                    'currency_symbol',
                    'reference_id',
                    'created_at',
                    'updated_at',
                ],
            ]);

        $data = $response->json('data');
        $this->assertEquals('completed', $data['status']);
        $this->assertEquals('tails', $data['joiner_side']); // Opposite of creator's heads
        $this->assertNotNull($data['result']);
        $this->assertNotNull($data['winner']);
    }

    public function test_cannot_join_own_game(): void
    {
        // Create a game
        Sanctum::actingAs($this->user);
        $createResponse = $this->postJson('/api/coinflip/create', [
            'bet' => 10.0,
            'side' => 'heads',
            'currency_id' => $this->currency->id,
        ]);

        $gameId = $createResponse->json('data.id');

        // Try to join own game
        $response = $this->postJson("/api/coinflip/{$gameId}/join");

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
            ]);
    }

    public function test_join_with_bot_requires_creator(): void
    {
        // Create a game with user1
        Sanctum::actingAs($this->user);
        $createResponse = $this->postJson('/api/coinflip/create', [
            'bet' => 10.0,
            'side' => 'heads',
            'currency_id' => $this->currency->id,
        ]);

        $gameId = $createResponse->json('data.id');

        // Try to call bot with user2 (not creator)
        Sanctum::actingAs($this->user2);
        $response = $this->postJson("/api/coinflip/{$gameId}/bot");

        $response->assertStatus(403)
            ->assertJson([
                'success' => false,
            ]);
    }

    public function test_join_with_bot_successfully(): void
    {
        // Create a game
        Sanctum::actingAs($this->user);
        $createResponse = $this->postJson('/api/coinflip/create', [
            'bet' => 10.0,
            'side' => 'heads',
            'currency_id' => $this->currency->id,
        ]);

        $gameId = $createResponse->json('data.id');

        // Join with bot
        $response = $this->postJson("/api/coinflip/{$gameId}/bot");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
            ]);

        $data = $response->json('data');
        $this->assertEquals('completed', $data['status']);
        $this->assertEquals('tails', $data['joiner_side']); // Opposite of creator's heads
        $this->assertNotNull($data['result']);
    }

    public function test_get_lobby_games(): void
    {
        // Create some games
        Sanctum::actingAs($this->user);
        $this->postJson('/api/coinflip/create', [
            'bet' => 10.0,
            'side' => 'heads',
            'currency_id' => $this->currency->id,
        ]);

        Sanctum::actingAs($this->user2);
        $response = $this->getJson('/api/coinflip/lobby');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => [
                        'id',
                        'creator',
                        'amount',
                        'amount_formatted',
                        'creator_side',
                        'status',
                        'currency_code',
                        'currency_symbol',
                        'reference_id',
                        'created_at',
                    ]
                ]
            ]);

        $games = $response->json('data');
        $this->assertCount(1, $games);
        $this->assertEquals('waiting', $games[0]['status']);
    }

    public function test_get_user_games(): void
    {
        // Create a game
        Sanctum::actingAs($this->user);
        $this->postJson('/api/coinflip/create', [
            'bet' => 10.0,
            'side' => 'heads',
            'currency_id' => $this->currency->id,
        ]);

        $response = $this->getJson('/api/coinflip/my-games');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => [
                        'id',
                        'creator',
                        'joiner',
                        'winner',
                        'amount',
                        'amount_formatted',
                        'creator_side',
                        'joiner_side',
                        'result',
                        'status',
                        'currency_code',
                        'currency_symbol',
                        'reference_id',
                        'created_at',
                        'updated_at',
                    ]
                ]
            ]);

        $games = $response->json('data');
        $this->assertCount(1, $games);
    }

    public function test_get_config(): void
    {
        $response = $this->getJson('/api/coinflip/config');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'house_edge' => 3.0,
                    'sides' => ['heads', 'tails'],
                ],
            ]);
    }

    public function test_get_minimum_bet(): void
    {
        $response = $this->postJson('/api/coinflip/minimum-bet', [
            'currency_id' => $this->currency->id,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonStructure([
                'success',
                'data' => [
                    'minimum_bet',
                    'minimum_bet_formatted',
                    'currency_code',
                    'currency_symbol',
                ],
            ]);
    }
}
