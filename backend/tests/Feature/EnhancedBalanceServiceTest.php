<?php

namespace Tests\Feature;

use App\Models\Currency;
use App\Models\User;
use App\Services\BalanceService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EnhancedBalanceServiceTest extends TestCase
{
    use RefreshDatabase;

    private BalanceService $balanceService;
    private User $user;
    private Currency $usdCurrency;
    private Currency $pointsCurrency;

    protected function setUp(): void
    {
        parent::setUp();

        $this->balanceService = new BalanceService();

        // Create test user
        $this->user = User::factory()->create();

        // Create test currencies
        $this->usdCurrency = Currency::create([
            'code' => 'USD',
            'decimals' => 2,
            'symbol' => '$',
        ]);

        $this->pointsCurrency = Currency::create([
            'code' => 'POINTS',
            'decimals' => 0,
            'symbol' => 'PTS',
        ]);
    }

    public function test_start_balance_chain(): void
    {
        $results = $this->balanceService->startBalanceChain($this->user);

        // Should create balance records for all currencies
        $this->assertArrayHasKey('USD', $results);
        $this->assertArrayHasKey('POINTS', $results);

        // Check USD balance
        $usdBalance = $results['USD'];
        $this->assertEquals(0, $usdBalance->balance);
        $this->assertEquals($this->user->id, $usdBalance->user_id);
        $this->assertEquals($this->usdCurrency->id, $usdBalance->currency_id);
        $this->assertNotEmpty($usdBalance->balance_hash);

        // Check POINTS balance
        $pointsBalance = $results['POINTS'];
        $this->assertEquals(0, $pointsBalance->balance);
        $this->assertEquals($this->user->id, $pointsBalance->user_id);
        $this->assertEquals($this->pointsCurrency->id, $pointsBalance->currency_id);
        $this->assertNotEmpty($pointsBalance->balance_hash);
    }

    public function test_credit_method(): void
    {
        $this->balanceService->startBalanceChain($this->user);

        $transaction = $this->balanceService->credit(
            $this->user,
            $this->usdCurrency,
            10000, // $100.00 in cents
            'deposit',
            'test-credit-123',
            ['source' => 'bank_transfer']
        );

        $this->assertEquals(10000, $transaction->amount);
        $this->assertEquals(0, $transaction->balance_before);
        $this->assertEquals(10000, $transaction->balance_after);
        $this->assertEquals('deposit', $transaction->type);
        $this->assertEquals('test-credit-123', $transaction->reference_id);
        $this->assertEquals(['source' => 'bank_transfer'], $transaction->meta);

        // Verify balance is updated
        $balance = $this->balanceService->getBalance($this->user, $this->usdCurrency);
        $this->assertEquals(10000, $balance);
    }

    public function test_debit_method(): void
    {
        $this->balanceService->startBalanceChain($this->user);

        // First credit some funds
        $this->balanceService->credit($this->user, $this->usdCurrency, 10000, 'deposit');

        $transaction = $this->balanceService->debit(
            $this->user,
            $this->usdCurrency,
            5000, // $50.00 in cents
            'withdrawal',
            'test-debit-456',
            ['destination' => 'bank_account']
        );

        $this->assertEquals(-5000, $transaction->amount);
        $this->assertEquals(10000, $transaction->balance_before);
        $this->assertEquals(5000, $transaction->balance_after);
        $this->assertEquals('withdrawal', $transaction->type);
        $this->assertEquals('test-debit-456', $transaction->reference_id);
        $this->assertEquals(['destination' => 'bank_account'], $transaction->meta);

        // Verify balance is updated
        $balance = $this->balanceService->getBalance($this->user, $this->usdCurrency);
        $this->assertEquals(5000, $balance);
    }

    public function test_credit_with_invalid_amount(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Credit amount must be positive');

        $this->balanceService->credit($this->user, $this->usdCurrency, -1000, 'deposit');
    }

    public function test_debit_with_invalid_amount(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Debit amount must be positive');

        $this->balanceService->debit($this->user, $this->usdCurrency, -1000, 'withdrawal');
    }

    public function test_debit_insufficient_balance(): void
    {
        $this->balanceService->startBalanceChain($this->user);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Insufficient balance');

        $this->balanceService->debit($this->user, $this->usdCurrency, 1000, 'withdrawal');
    }

    public function test_get_balance_method(): void
    {
        // Test with no balance chain started
        $balance = $this->balanceService->getBalance($this->user, $this->usdCurrency);
        $this->assertEquals(0, $balance);

        // Start balance chain and test again
        $this->balanceService->startBalanceChain($this->user);
        $balance = $this->balanceService->getBalance($this->user, $this->usdCurrency);
        $this->assertEquals(0, $balance);

        // Credit some funds and test
        $this->balanceService->credit($this->user, $this->usdCurrency, 15000, 'deposit');
        $balance = $this->balanceService->getBalance($this->user, $this->usdCurrency);
        $this->assertEquals(15000, $balance);
    }

    public function test_get_balances_method(): void
    {
        $this->balanceService->startBalanceChain($this->user);

        // Credit different amounts to different currencies
        $this->balanceService->credit($this->user, $this->usdCurrency, 10000, 'deposit');
        $this->balanceService->credit($this->user, $this->pointsCurrency, 500, 'reward');

        $balances = $this->balanceService->getBalances($this->user);

        $this->assertArrayHasKey('USD', $balances);
        $this->assertArrayHasKey('POINTS', $balances);
        $this->assertEquals(10000, $balances['USD']);
        $this->assertEquals(500, $balances['POINTS']);
    }

    public function test_transaction_hash_integrity(): void
    {
        $this->balanceService->startBalanceChain($this->user);

        $transaction = $this->balanceService->credit($this->user, $this->usdCurrency, 10000, 'deposit');

        // Verify transaction integrity
        $this->assertTrue($this->balanceService->verifyTransactionIntegrity($transaction));

        // Verify hash is 64 characters (SHA-256)
        $this->assertEquals(64, strlen($transaction->hash));
    }

    public function test_balance_hash_integrity(): void
    {
        $this->balanceService->startBalanceChain($this->user);

        $this->balanceService->credit($this->user, $this->usdCurrency, 10000, 'deposit');

        $balanceRecord = \App\Models\UserBalance::where('user_id', $this->user->id)
            ->where('currency_id', $this->usdCurrency->id)
            ->first();

        // Verify balance hash is 64 characters (SHA-256)
        $this->assertEquals(64, strlen($balanceRecord->balance_hash));
    }

    public function test_transaction_chain_integrity(): void
    {
        $this->balanceService->startBalanceChain($this->user);

        // Create multiple transactions
        $tx1 = $this->balanceService->credit($this->user, $this->usdCurrency, 1000, 'deposit');
        $tx2 = $this->balanceService->credit($this->user, $this->usdCurrency, 500, 'deposit');
        $tx3 = $this->balanceService->debit($this->user, $this->usdCurrency, 200, 'withdrawal');

        // Verify each transaction integrity
        $this->assertTrue($this->balanceService->verifyTransactionIntegrity($tx1));
        $this->assertTrue($this->balanceService->verifyTransactionIntegrity($tx2));
        $this->assertTrue($this->balanceService->verifyTransactionIntegrity($tx3));

        // Verify the entire chain
        $this->assertTrue($this->balanceService->verifyUserTransactionChain($this->user, $this->usdCurrency));

        // Verify chain links
        $this->assertEquals($tx1->hash, $tx2->previous_hash);
        $this->assertEquals($tx2->hash, $tx3->previous_hash);
    }

    public function test_multiple_currencies_independence(): void
    {
        $this->balanceService->startBalanceChain($this->user);

        // Credit different currencies
        $usdTx = $this->balanceService->credit($this->user, $this->usdCurrency, 10000, 'deposit');
        $pointsTx = $this->balanceService->credit($this->user, $this->pointsCurrency, 500, 'reward');

        // Verify balances are independent
        $this->assertEquals(10000, $this->balanceService->getBalance($this->user, $this->usdCurrency));
        $this->assertEquals(500, $this->balanceService->getBalance($this->user, $this->pointsCurrency));

        // Verify transaction chains are independent
        $this->assertTrue($this->balanceService->verifyUserTransactionChain($this->user, $this->usdCurrency));
        $this->assertTrue($this->balanceService->verifyUserTransactionChain($this->user, $this->pointsCurrency));

        // Verify different genesis hashes
        $this->assertNotEquals($usdTx->previous_hash, $pointsTx->previous_hash);
    }
}
