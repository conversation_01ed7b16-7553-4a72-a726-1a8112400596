<?php

namespace Tests\Feature;

use App\Models\Currency;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class BalanceApiTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Currency $usdCurrency;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->usdCurrency = Currency::create([
            'code' => 'USD',
            'decimals' => 2,
            'symbol' => '$',
        ]);
    }

    public function test_get_currencies(): void
    {
        $response = $this->getJson('/api/currencies');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => [
                        'id',
                        'code',
                        'symbol',
                        'decimals',
                    ]
                ]
            ]);
    }

    public function test_get_specific_currency(): void
    {
        $response = $this->getJson("/api/currencies/{$this->usdCurrency->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'id' => $this->usdCurrency->id,
                    'code' => 'USD',
                    'symbol' => '$',
                    'decimals' => 2,
                ]
            ]);
    }

    public function test_get_balances_requires_authentication(): void
    {
        $response = $this->getJson('/api/balances');
        $response->assertStatus(401);
    }

    public function test_get_balances_authenticated(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/balances');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => []
            ]);
    }

    public function test_get_specific_balance(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->getJson("/api/balances/{$this->usdCurrency->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'currency' => [
                        'id' => $this->usdCurrency->id,
                        'code' => 'USD',
                    ],
                    'balance' => 0,
                    'balance_formatted' => 0.0,
                ]
            ]);
    }

    public function test_process_transaction(): void
    {
        Sanctum::actingAs($this->user);

        $transactionData = [
            'currency_id' => $this->usdCurrency->id,
            'amount' => 100.50,
            'type' => 'deposit',
            'reference_id' => 'test-deposit-123',
            'meta' => ['source' => 'test'],
        ];

        $response = $this->postJson('/api/transactions', $transactionData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'transaction_id',
                    'amount',
                    'amount_formatted',
                    'balance_before',
                    'balance_before_formatted',
                    'balance_after',
                    'balance_after_formatted',
                    'type',
                    'reference_id',
                    'created_at',
                ]
            ])
            ->assertJson([
                'success' => true,
                'data' => [
                    'amount_formatted' => 100.50,
                    'balance_before_formatted' => 0.0,
                    'balance_after_formatted' => 100.50,
                    'type' => 'deposit',
                    'reference_id' => 'test-deposit-123',
                ]
            ]);
    }

    public function test_process_transaction_validation(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/transactions', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['currency_id', 'amount', 'type']);
    }

    public function test_get_transaction_history(): void
    {
        Sanctum::actingAs($this->user);

        // First create a transaction
        $this->postJson('/api/transactions', [
            'currency_id' => $this->usdCurrency->id,
            'amount' => 50.0,
            'type' => 'deposit',
        ]);

        $response = $this->getJson("/api/transactions/{$this->usdCurrency->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => [
                        'id',
                        'amount',
                        'amount_formatted',
                        'balance_before',
                        'balance_before_formatted',
                        'balance_after',
                        'balance_after_formatted',
                        'type',
                        'reference_id',
                        'meta',
                        'created_at',
                    ]
                ],
                'pagination' => [
                    'limit',
                    'offset',
                    'total',
                ]
            ]);
    }

    public function test_insufficient_balance_transaction(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/transactions', [
            'currency_id' => $this->usdCurrency->id,
            'amount' => -100.0, // Trying to withdraw from zero balance
            'type' => 'withdrawal',
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Insufficient balance',
            ]);
    }
}
