<?php

namespace Tests\Feature;

use App\Models\Currency;
use App\Models\Transaction;
use App\Models\User;
use App\Models\UserBalance;
use App\Services\AuditService;
use App\Services\BalanceService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AuditServiceTest extends TestCase
{
    use RefreshDatabase;

    private AuditService $auditService;
    private BalanceService $balanceService;
    private User $user;
    private Currency $currency;

    protected function setUp(): void
    {
        parent::setUp();

        $this->balanceService = new BalanceService();
        $this->auditService = new AuditService($this->balanceService);

        $this->user = User::factory()->create();
        $this->currency = Currency::create([
            'code' => 'USD',
            'decimals' => 2,
            'symbol' => '$',
        ]);
    }

    public function test_verify_transaction_chain_with_no_transactions(): void
    {
        $result = $this->auditService->verifyTransaction<PERSON>hain($this->user, $this->currency);

        $this->assertTrue($result['success']);
        $this->assertEquals('No transactions found for verification', $result['message']);
        $this->assertEquals(0, $result['transactions_verified']);
    }

    public function test_verify_transaction_chain_with_valid_transactions(): void
    {
        // Create some transactions
        $this->balanceService->credit($this->user, $this->currency, 1000, 'deposit');
        $this->balanceService->credit($this->user, $this->currency, 500, 'deposit');
        $this->balanceService->debit($this->user, $this->currency, 200, 'withdrawal');

        $result = $this->auditService->verifyTransactionChain($this->user, $this->currency);

        $this->assertTrue($result['success']);
        $this->assertEquals('Transaction chain verified successfully', $result['message']);
        $this->assertEquals(4, $result['transactions_verified']); // 3 + 1 genesis
    }

    public function test_verify_transaction_chain_with_corrupted_hash(): void
    {
        // Create a transaction
        $transaction = $this->balanceService->credit($this->user, $this->currency, 1000, 'deposit');

        // Corrupt the transaction hash
        $transaction->hash = 'corrupted_hash';
        $transaction->save();

        $result = $this->auditService->verifyTransactionChain($this->user, $this->currency);

        $this->assertFalse($result['success']);
        $this->assertEquals('Transaction hash mismatch', $result['error']);
        $this->assertEquals($transaction->id, $result['transaction_id']);
    }

    public function test_verify_balance_hash_with_missing_balance(): void
    {
        $result = $this->auditService->verifyBalanceHash($this->user, $this->currency);

        $this->assertTrue($result['success']);
        $this->assertEquals('Zero balance and genesis transaction created', $result['message']);
        $this->assertTrue($result['seeded']);

        // Verify that balance and genesis transaction were created
        $balance = UserBalance::where('user_id', $this->user->id)
            ->where('currency_id', $this->currency->id)
            ->first();
        $this->assertNotNull($balance);
        $this->assertEquals(0, $balance->balance);

        $genesisTransaction = Transaction::where('user_id', $this->user->id)
            ->where('currency_id', $this->currency->id)
            ->where('type', 'genesis')
            ->first();
        $this->assertNotNull($genesisTransaction);
        $this->assertEquals(0, $genesisTransaction->amount);
    }

    public function test_verify_balance_hash_with_existing_valid_balance(): void
    {
        // Create some transactions first
        $this->balanceService->credit($this->user, $this->currency, 1000, 'deposit');

        $result = $this->auditService->verifyBalanceHash($this->user, $this->currency);

        $this->assertTrue($result['success']);
        $this->assertEquals('Balance hash verified successfully', $result['message']);
        $this->assertFalse(isset($result['seeded']));
    }

    public function test_verify_balance_hash_with_corrupted_hash(): void
    {
        // Create a transaction to establish balance
        $this->balanceService->credit($this->user, $this->currency, 1000, 'deposit');

        // Corrupt the balance hash
        $balance = UserBalance::where('user_id', $this->user->id)
            ->where('currency_id', $this->currency->id)
            ->first();
        $balance->balance_hash = 'corrupted_hash';
        $balance->save();

        $result = $this->auditService->verifyBalanceHash($this->user, $this->currency);

        $this->assertFalse($result['success']);
        $this->assertEquals('Balance hash mismatch', $result['error']);
    }

    public function test_recalculate_balance_with_correct_balance(): void
    {
        // Create transactions
        $this->balanceService->credit($this->user, $this->currency, 1000, 'deposit');
        $this->balanceService->credit($this->user, $this->currency, 500, 'deposit');
        $this->balanceService->debit($this->user, $this->currency, 200, 'withdrawal');

        $result = $this->auditService->recalculateBalance($this->user, $this->currency);

        $this->assertTrue($result['success']);
        $this->assertEquals('Balance calculation verified', $result['message']);
        $this->assertEquals(1300, $result['calculated_balance']); // 1000 + 500 - 200
        $this->assertEquals(1300, $result['stored_balance']);
        $this->assertEquals(0, $result['difference']);
    }

    public function test_recalculate_balance_with_incorrect_balance(): void
    {
        // Create a transaction
        $this->balanceService->credit($this->user, $this->currency, 1000, 'deposit');

        // Manually corrupt the stored balance
        $balance = UserBalance::where('user_id', $this->user->id)
            ->where('currency_id', $this->currency->id)
            ->first();
        $balance->balance = 500; // Should be 1000
        $balance->save();

        $result = $this->auditService->recalculateBalance($this->user, $this->currency);

        $this->assertFalse($result['success']);
        $this->assertEquals('Balance mismatch detected', $result['message']);
        $this->assertEquals(1000, $result['calculated_balance']);
        $this->assertEquals(500, $result['stored_balance']);
        $this->assertEquals(500, $result['difference']);
    }

    public function test_recalculate_balance_with_no_balance_record(): void
    {
        // Create a transaction directly without using BalanceService
        Transaction::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 1000,
            'balance_before' => 0,
            'balance_after' => 1000,
            'type' => 'deposit',
            'previous_hash' => hash('sha256', "genesis_{$this->user->id}_{$this->currency->id}"),
            'hash' => 'test_hash',
            'created_at' => now(),
        ]);

        $result = $this->auditService->recalculateBalance($this->user, $this->currency);

        $this->assertFalse($result['success']);
        $this->assertEquals('No balance record found', $result['error']);
        $this->assertEquals(1000, $result['calculated_balance']);
        $this->assertNull($result['stored_balance']);
    }

    public function test_comprehensive_verification_all_pass(): void
    {
        // Create some transactions
        $this->balanceService->credit($this->user, $this->currency, 1000, 'deposit');
        $this->balanceService->debit($this->user, $this->currency, 300, 'withdrawal');

        $result = $this->auditService->comprehensiveVerification($this->user, $this->currency);

        $this->assertTrue($result['overall_success']);
        $this->assertTrue($result['transaction_chain']['success']);
        $this->assertTrue($result['balance_hash']['success']);
        $this->assertTrue($result['balance_calculation']['success']);
        $this->assertEquals($this->user->id, $result['user_id']);
        $this->assertEquals($this->currency->code, $result['currency_code']);
    }

    public function test_comprehensive_verification_with_seeding(): void
    {
        // No existing transactions or balance
        $result = $this->auditService->comprehensiveVerification($this->user, $this->currency);

        $this->assertTrue($result['overall_success']);
        $this->assertTrue($result['transaction_chain']['success']);
        $this->assertTrue($result['balance_hash']['success']);
        $this->assertTrue($result['balance_hash']['seeded']);
        $this->assertTrue($result['balance_calculation']['success']);
    }

    public function test_comprehensive_verification_with_failures(): void
    {
        // Create a transaction and then corrupt data
        $this->balanceService->credit($this->user, $this->currency, 1000, 'deposit');

        // Corrupt transaction hash
        $transaction = Transaction::where('user_id', $this->user->id)
            ->where('currency_id', $this->currency->id)
            ->where('type', '!=', 'genesis')
            ->first();
        $transaction->hash = 'corrupted';
        $transaction->save();

        $result = $this->auditService->comprehensiveVerification($this->user, $this->currency);

        $this->assertFalse($result['overall_success']);
        $this->assertFalse($result['transaction_chain']['success']);
        // Balance hash and calculation might still pass depending on corruption
    }

    public function test_seeding_is_idempotent(): void
    {
        // First seeding
        $result1 = $this->auditService->verifyBalanceHash($this->user, $this->currency);
        $this->assertTrue($result1['success']);
        $this->assertTrue($result1['seeded']);

        $balanceId1 = $result1['balance_id'];

        // Second attempt should not create new records
        $result2 = $this->auditService->verifyBalanceHash($this->user, $this->currency);
        $this->assertTrue($result2['success']);
        $this->assertFalse(isset($result2['seeded']));

        // Should have same balance record
        $this->assertEquals($balanceId1, $result2['balance_id']);
    }

    public function test_auto_seeding_only_affects_empty_combinations(): void
    {
        // Create another user and currency
        $user2 = User::factory()->create();
        $currency2 = Currency::create(['code' => 'POINTS', 'decimals' => 0, 'symbol' => 'PTS']);

        // Seed for user1 + currency1
        $this->auditService->verifyBalanceHash($this->user, $this->currency);

        // Verify only that combination was affected
        $this->assertDatabaseHas('user_balances', [
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
        ]);

        $this->assertDatabaseMissing('user_balances', [
            'user_id' => $user2->id,
            'currency_id' => $this->currency->id,
        ]);

        $this->assertDatabaseMissing('user_balances', [
            'user_id' => $this->user->id,
            'currency_id' => $currency2->id,
        ]);
    }
}
