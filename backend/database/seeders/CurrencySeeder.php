<?php

namespace Database\Seeders;

use App\Models\Currency;
use Illuminate\Database\Seeder;

class CurrencySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $currencies = [
            [
                'code' => 'USD',
                'decimals' => 2,
                'symbol' => '$',
            ],
            [
                'code' => 'USDT',
                'decimals' => 6,
                'symbol' => 'USDT',
            ],
            [
                'code' => 'POINTS',
                'decimals' => 0,
                'symbol' => 'PTS',
            ],
        ];

        foreach ($currencies as $currencyData) {
            Currency::updateOrCreate(
                ['code' => $currencyData['code']],
                $currencyData
            );
        }
    }
}
