<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dice_games', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('currency_id')->constrained()->onDelete('cascade');
            $table->integer('bet_amount'); // Amount in minor units (cents, etc.)
            $table->decimal('threshold', 5, 2); // 1.00 to 99.99
            $table->enum('direction', ['over', 'under']);
            $table->decimal('roll_result', 5, 2); // 0.00 to 99.99
            $table->boolean('win');
            $table->integer('profit'); // Profit/loss in minor units (can be negative)
            $table->decimal('multiplier', 8, 4); // Multiplier used for the game
            $table->decimal('win_chance', 5, 2); // Win chance percentage
            $table->integer('balance_before'); // User balance before the game
            $table->integer('balance_after'); // User balance after the game
            $table->string('reference_id')->unique(); // Unique reference for this game
            $table->json('meta')->nullable(); // Additional metadata
            $table->timestamp('created_at');

            // Indexes for performance
            $table->index(['user_id', 'created_at']);
            $table->index(['currency_id', 'created_at']);
            $table->index('reference_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dice_games');
    }
};
