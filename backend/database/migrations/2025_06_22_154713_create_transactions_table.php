<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('currency_id')->constrained()->onDelete('cascade');
            $table->bigInteger('amount'); // Signed amount (positive or negative)
            $table->bigInteger('balance_before');
            $table->bigInteger('balance_after');
            $table->string('type');
            $table->string('reference_id')->nullable();
            $table->json('meta')->nullable();
            $table->char('previous_hash', 64);
            $table->char('hash', 64);
            $table->timestamp('created_at');

            // Index for efficient queries
            $table->index(['user_id', 'currency_id', 'created_at']);

            // Index for hash chain verification
            $table->index(['user_id', 'currency_id', 'id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
