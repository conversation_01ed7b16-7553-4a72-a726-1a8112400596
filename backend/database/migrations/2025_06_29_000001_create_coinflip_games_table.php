<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coinflip_games', function (Blueprint $table) {
            $table->id();
            $table->foreignId('creator_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('joiner_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->foreignId('currency_id')->constrained()->onDelete('cascade');
            $table->integer('amount'); // Amount in minor units (cents, etc.)
            $table->enum('creator_side', ['heads', 'tails']);
            $table->enum('joiner_side', ['heads', 'tails'])->nullable();
            $table->enum('result', ['heads', 'tails'])->nullable(); // 0 = heads, 1 = tails
            $table->enum('status', ['waiting', 'completed', 'cancelled'])->default('waiting');
            $table->foreignId('winner_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->integer('creator_balance_before')->nullable(); // Balance before game for creator
            $table->integer('creator_balance_after')->nullable(); // Balance after game for creator
            $table->integer('joiner_balance_before')->nullable(); // Balance before game for joiner
            $table->integer('joiner_balance_after')->nullable(); // Balance after game for joiner
            $table->integer('house_profit'); // House profit in minor units (9% of total pot)
            $table->integer('winner_profit')->nullable(); // Winner profit in minor units (91% of total pot)
            $table->string('reference_id')->unique();
            $table->json('meta')->nullable(); // Additional metadata
            $table->timestamp('created_at');
            $table->timestamp('updated_at')->nullable();

            // Indexes for performance
            $table->index(['status', 'created_at']);
            $table->index(['creator_id', 'created_at']);
            $table->index(['joiner_id', 'created_at']);
            $table->index('reference_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coinflip_games');
    }
};
