<?php

namespace Database\Factories;

use App\Models\Currency;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Currency>
 */
class CurrencyFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Currency::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $currencies = [
            ['code' => 'USD', 'decimals' => 2, 'symbol' => '$'],
            ['code' => 'USDT', 'decimals' => 6, 'symbol' => 'USDT'],
            ['code' => 'POINTS', 'decimals' => 0, 'symbol' => 'PTS'],
        ];

        $currency = $this->faker->randomElement($currencies);

        return [
            'code' => $currency['code'],
            'decimals' => $currency['decimals'],
            'symbol' => $currency['symbol'],
        ];
    }

    /**
     * Create a USD currency.
     */
    public function usd(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'code' => 'USD',
                'decimals' => 2,
                'symbol' => '$',
            ];
        });
    }

    /**
     * Create a USDT currency.
     */
    public function usdt(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'code' => 'USDT',
                'decimals' => 6,
                'symbol' => 'USDT',
            ];
        });
    }

    /**
     * Create a POINTS currency.
     */
    public function points(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'code' => 'POINTS',
                'decimals' => 0,
                'symbol' => 'PTS',
            ];
        });
    }
}
