<?php

namespace Database\Factories;

use App\Models\CoinflipGame;
use App\Models\Currency;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CoinflipGame>
 */
class CoinflipGameFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CoinflipGame::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'creator_id' => User::factory(),
            'currency_id' => Currency::factory(),
            'amount' => $this->faker->numberBetween(100, 10000), // $1.00 to $100.00 in cents
            'creator_side' => $this->faker->randomElement(['heads', 'tails']),
            'status' => 'waiting',
            'house_profit' => 0,
            'reference_id' => CoinflipGame::generateReferenceId(),
            'meta' => [
                'house_edge' => 9.0,
                'timestamp' => now()->timestamp,
            ],
            'created_at' => now(),
        ];
    }

    /**
     * Indicate that the game is completed.
     */
    public function completed(): static
    {
        return $this->state(function (array $attributes) {
            $result = $this->faker->randomElement(['heads', 'tails']);
            $winnerId = $result === $attributes['creator_side'] ? $attributes['creator_id'] : null;
            
            // If there's a joiner, they could be the winner
            if (!$winnerId && isset($attributes['joiner_id'])) {
                $winnerId = $attributes['joiner_id'];
            }

            $totalPot = $attributes['amount'] * 2;
            $houseProfit = (int) round($totalPot * 0.09);
            $winnerProfit = $totalPot - $houseProfit;

            return [
                'joiner_id' => $attributes['joiner_id'] ?? User::factory(),
                'joiner_side' => $attributes['creator_side'] === 'heads' ? 'tails' : 'heads',
                'result' => $result,
                'status' => 'completed',
                'winner_id' => $winnerId,
                'house_profit' => $houseProfit,
                'winner_profit' => $winnerProfit,
                'updated_at' => now(),
            ];
        });
    }

    /**
     * Indicate that the game is waiting for a joiner.
     */
    public function waiting(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'waiting',
                'joiner_id' => null,
                'joiner_side' => null,
                'result' => null,
                'winner_id' => null,
                'house_profit' => 0,
                'winner_profit' => null,
                'updated_at' => null,
            ];
        });
    }
}
