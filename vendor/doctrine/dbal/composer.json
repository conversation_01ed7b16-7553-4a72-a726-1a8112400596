{"name": "doctrine/dbal", "type": "library", "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "keywords": ["abstraction", "database", "dbal", "db2", "ma<PERSON>b", "mssql", "mysql", "pgsql", "postgresql", "oci8", "oracle", "pdo", "queryobject", "sasql", "sql", "sqlite", "sqlserver", "sqlsrv"], "homepage": "https://www.doctrine-project.org/projects/dbal.html", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "doctrine/deprecations": "^0.5.3|^1", "psr/cache": "^1|^2|^3", "psr/log": "^1|^2|^3"}, "require-dev": {"doctrine/coding-standard": "13.0.0", "fig/log-test": "^1", "jetbrains/phpstorm-stubs": "2023.2", "phpstan/phpstan": "2.1.17", "phpstan/phpstan-phpunit": "2.0.6", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "10.5.46", "slevomat/coding-standard": "8.16.2", "squizlabs/php_codesniffer": "3.13.1", "symfony/cache": "^6.3.8|^7.0", "symfony/console": "^5.4|^6.3|^7.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "config": {"sort-packages": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "composer/package-versions-deprecated": true}}, "autoload": {"psr-4": {"Doctrine\\DBAL\\": "src"}}, "autoload-dev": {"psr-4": {"Doctrine\\DBAL\\Tests\\": "tests"}}}