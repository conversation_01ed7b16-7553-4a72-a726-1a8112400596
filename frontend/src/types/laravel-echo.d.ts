declare module 'laravel-echo' {
  export interface Channel {
    listen(event: string, callback: Function): Channel;
    stopListening(event: string, callback?: Function): Channel;
    notification(callback: Function): Channel;
    error(callback: Function): Channel;
  }

  export interface PresenceChannel extends Channel {
    here(callback: Function): PresenceChannel;
    joining(callback: Function): PresenceChannel;
    leaving(callback: Function): PresenceChannel;
    whisper(eventName: string, data: any): PresenceChannel;
  }

  export interface EchoOptions {
    broadcaster?: string;
    host?: string;
    key?: string;
    wsHost?: string;
    wsPort?: number;
    wssPort?: number;
    forceTLS?: boolean;
    disableStats?: boolean;
    encrypted?: boolean;
    cluster?: string;
    authEndpoint?: string;
    auth?: {
      headers?: any;
    };
    authorizer?: (channel: any, options: any) => any;
    enabledTransports?: string[];
    [key: string]: any;
  }

  export default class Echo {
    constructor(options: EchoOptions);
    channel(channel: string): Channel;
    private(channel: string): Channel;
    join(channel: string): PresenceChannel;
    leave(channel: string): void;
    leaveChannel(channel: string): void;
    disconnect(): void;
    connect(): void;
    socketId(): string;
    connector: any;
  }
}
