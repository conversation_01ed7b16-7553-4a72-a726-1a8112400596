import { useToast } from 'vue-toastification'

export interface ToastNotification {
  type: 'success' | 'error' | 'info' | 'warning'
  title: string
  message?: string
}

class ToastService {
  private toast: any = null

  private getToast() {
    if (!this.toast) {
      this.toast = useToast()
    }
    return this.toast
  }

  success(title: string, message?: string) {
    console.log('Toast Service: Showing success toast:', title, message)
    this.getToast().success(message ? `${title}: ${message}` : title)
  }

  error(title: string, message?: string) {
    console.log('Toast Service: Showing error toast:', title, message)
    this.getToast().error(message ? `${title}: ${message}` : title)
  }

  info(title: string, message?: string) {
    console.log('Toast Service: Showing info toast:', title, message)
    this.getToast().info(message ? `${title}: ${message}` : title)
  }

  warning(title: string, message?: string) {
    console.log('Toast Service: Showing warning toast:', title, message)
    this.getToast().warning(message ? `${title}: ${message}` : title)
  }

  // Handle notification from WebSocket broadcast
  handleNotification(notification: ToastNotification) {
    console.log('Toast Service: Handling notification:', notification)
    switch (notification.type) {
      case 'success':
        this.success(notification.title, notification.message)
        break
      case 'error':
        this.error(notification.title, notification.message)
        break
      case 'info':
        this.info(notification.title, notification.message)
        break
      case 'warning':
        this.warning(notification.title, notification.message)
        break
      default:
        console.warn('Toast Service: Unknown notification type:', notification.type)
    }
  }
}

export const toastService = new ToastService()
export default toastService
