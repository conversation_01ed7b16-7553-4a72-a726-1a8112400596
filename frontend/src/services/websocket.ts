import echo from '@/services/echo'

interface WebSocketSubscriber {
  event: string
  callback: (data: any) => void
}

class WebSocketService {
  private static instance: WebSocketService
  private subscribers: Map<string, WebSocketSubscriber[]> = new Map()
  private activeChannels: Set<string> = new Set()
  private currentUserId: number | null = null

  private constructor() {}

  static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService()
    }
    return WebSocketService.instance
  }

  /**
   * Subscribe to a specific event on the user's private channel
   */
  subscribe(event: string, callback: (data: any) => void): void {
    if (!this.subscribers.has(event)) {
      this.subscribers.set(event, [])
    }

    this.subscribers.get(event)!.push({ event, callback })

    // If we have a user and this is the first subscriber for this event,
    // set up the listener on the channel
    if (this.currentUserId && this.subscribers.get(event)!.length === 1) {
      this.setupEventListener(event)
    }
  }

  /**
   * Unsubscribe from a specific event
   */
  unsubscribe(event: string, callback: (data: any) => void): void {
    const eventSubscribers = this.subscribers.get(event)
    if (!eventSubscribers) return

    const index = eventSubscribers.findIndex(sub => sub.callback === callback)
    if (index > -1) {
      eventSubscribers.splice(index, 1)

      // If no more subscribers for this event, remove the listener
      if (eventSubscribers.length === 0) {
        this.removeEventListener(event)
        this.subscribers.delete(event)
      }
    }
  }

  /**
   * Initialize WebSocket connection for a user
   */
  initializeForUser(userId: number): void {
    if (this.currentUserId === userId) {
      return // Already initialized for this user
    }

    // Clean up previous user's connections
    this.cleanup()

    this.currentUserId = userId
    const channelName = `user.${userId}`

    try {
      // Subscribe to the private channel
      const channel = echo.private(channelName)
      this.activeChannels.add(channelName)

      // Set up listeners for all currently subscribed events
      for (const event of this.subscribers.keys()) {
        this.setupEventListener(event)
      }

      console.log(`WebSocket Service: Initialized for user ${userId}`)
    } catch (error) {
      console.error('WebSocket Service: Failed to initialize for user:', error)
    }
  }

  /**
   * Clean up all WebSocket connections
   */
  cleanup(): void {
    if (!this.currentUserId) return

    try {
      // Leave all active channels
      for (const channelName of this.activeChannels) {
        echo.leave(channelName)
      }

      this.activeChannels.clear()
      this.currentUserId = null

      console.log('WebSocket Service: Cleaned up connections')
    } catch (error) {
      console.error('WebSocket Service: Failed to cleanup:', error)
    }
  }

  /**
   * Set up event listener on the current user's channel
   */
  private setupEventListener(event: string): void {
    if (!this.currentUserId) return

    const channelName = `user.${this.currentUserId}`

    try {
      // For custom events, Laravel Echo expects a dot prefix
      const echoEventName = event.startsWith('.') ? event : `.${event}`

      echo.private(channelName).listen(echoEventName, (data: any) => {
        console.log(`WebSocket Service: Received ${event}:`, data)
        this.notifySubscribers(event, data)
      })
    } catch (error) {
      console.error(`WebSocket Service: Failed to setup listener for ${event}:`, error)
    }
  }

  /**
   * Remove event listener (Note: Laravel Echo doesn't have a direct way to remove specific listeners)
   */
  private removeEventListener(event: string): void {
    // Laravel Echo doesn't provide a way to remove specific event listeners
    // The cleanup happens when we leave the channel entirely
    console.log(`WebSocket Service: Event ${event} no longer has subscribers`)
  }

  /**
   * Notify all subscribers of an event
   */
  private notifySubscribers(event: string, data: any): void {
    const eventSubscribers = this.subscribers.get(event)
    if (!eventSubscribers) return

    eventSubscribers.forEach(subscriber => {
      try {
        subscriber.callback(data)
      } catch (error) {
        console.error(`WebSocket Service: Error in subscriber callback for ${event}:`, error)
      }
    })
  }

  /**
   * Get current user ID
   */
  getCurrentUserId(): number | null {
    return this.currentUserId
  }

  /**
   * Check if service is initialized for a user
   */
  isInitialized(): boolean {
    return this.currentUserId !== null
  }
}

// Export singleton instance
export default WebSocketService.getInstance()
