<script setup lang="ts">
import RegisterForm from '@/components/RegisterForm.vue';
</script>

<template>
  <div class="register-view">
    <div class="register-container">
      <h1>Register</h1>
      <RegisterForm />
    </div>
  </div>
</template>

<style scoped>
.register-view {
  min-height: calc(100vh - var(--header-height, 80px));
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: #f8f8f8;
}

.register-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  width: 100%;
  max-width: 600px;
}

.register-container h1 {
  color: #333;
  font-size: 2rem;
  margin: 0;
  text-align: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .register-view {
    padding: 1rem;
  }

  .register-container h1 {
    font-size: 1.5rem;
  }
}
</style>
