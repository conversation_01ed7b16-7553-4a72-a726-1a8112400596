<script setup lang="ts">
import LoginForm from '@/components/LoginForm.vue';
</script>

<template>
  <div class="login-view">
    <div class="login-container">
      <h1>Login</h1>
      <LoginForm />
    </div>
  </div>
</template>

<style scoped>
.login-view {
  min-height: calc(100vh - var(--header-height, 80px));
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: #f8f8f8;
}

.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  width: 100%;
  max-width: 600px;
}

.login-container h1 {
  color: #333;
  font-size: 2rem;
  margin: 0;
  text-align: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .login-view {
    padding: 1rem;
  }

  .login-container h1 {
    font-size: 1.5rem;
  }
}
</style>
