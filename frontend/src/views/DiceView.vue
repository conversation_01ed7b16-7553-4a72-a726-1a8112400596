<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useBalanceStore } from '@/stores/balance'
import { useDiceStore } from '@/stores/dice'

const authStore = useAuthStore()
const balanceStore = useBalanceStore()
const diceStore = useDiceStore()

// Game state
const betAmount = ref<number>(1.00)
const threshold = ref<number>(50.00)
const direction = ref<'over' | 'under'>('over')
const isRolling = ref<boolean>(false)

// Auto-bet state
const isAutoBetting = ref<boolean>(false)
const autoBetRolls = ref<number>(10)
const autoBetMode = ref<'manual' | 'auto'>('manual')
const stopOnWin = ref<number>(0)
const stopOnLoss = ref<number>(0)
const sessionProfit = ref<number>(0)
const currentBetCount = ref<number>(0)

// Computed values
const winChance = computed(() => {
  if (direction.value === 'over') {
    return 100.0 - threshold.value
  } else {
    return threshold.value
  }
})

const multiplier = computed(() => {
  if (winChance.value <= 0) return 0
  const houseEdge = diceStore.config?.house_edge || 1.0
  return (100.0 - houseEdge) / winChance.value
})

const estimatedProfit = computed(() => {
  return betAmount.value * multiplier.value - betAmount.value
})

const thresholdPercent = computed(() => {
  return `${threshold.value}%`
})

const maxBet = computed(() => {
  if (!balanceStore.currentBalance) return 0
  return balanceStore.currentBalance.formatted
})

const minBet = computed(() => {
  return diceStore.minimumBetInfo?.minimum_bet || 0.01
})

const betStep = computed(() => {
  const decimals = balanceStore.currentBalance?.decimals || 2
  return 1 / Math.pow(10, decimals)
})

const canRoll = computed(() => {
  const minThreshold = diceStore.config?.min_threshold || 2.00
  const maxThreshold = diceStore.config?.max_threshold || 98.00

  return betAmount.value >= minBet.value &&
         betAmount.value <= maxBet.value &&
         threshold.value >= minThreshold &&
         threshold.value <= maxThreshold &&
         winChance.value > 0 &&
         !isRolling.value
})



// Methods

const roll = async () => {
  if (!canRoll.value) return

  isRolling.value = true

  try {
    // Get currency ID from the currencies API
    let currencyId = null
    if (balanceStore.currentBalance) {
      const currenciesResponse = await fetch(`${import.meta.env.VITE_API_URL}/api/currencies`)
      const currenciesData = await currenciesResponse.json()

      if (currenciesData.success) {
        const currency = currenciesData.data.find((c: any) => c.code === balanceStore.currentBalance?.currency_code)
        currencyId = currency?.id
      }
    }

    await diceStore.roll({
      bet: betAmount.value,
      threshold: threshold.value,
      direction: direction.value,
      currency_id: currencyId
    })
  } catch (error) {
    console.error('Roll failed:', error)
  } finally {
    isRolling.value = false
  }
}

const setMaxBet = () => {
  betAmount.value = maxBet.value
}

const setMinBet = () => {
  betAmount.value = minBet.value
}

const halveBet = () => {
  const newAmount = betAmount.value / 2
  betAmount.value = Math.max(newAmount, minBet.value)
}

const doubleBet = () => {
  const newAmount = betAmount.value * 2
  betAmount.value = Math.min(newAmount, maxBet.value)
}

const rollDice = roll

const canBet = computed(() => {
  return canRoll.value && !isAutoBetting.value
})

// Input validation methods
const formatBetAmount = () => {
  const decimals = balanceStore.currentBalance?.decimals || 2
  betAmount.value = parseFloat(betAmount.value.toFixed(decimals))
}

const formatAutoBetRolls = () => {
  autoBetRolls.value = Math.floor(Math.max(0, autoBetRolls.value))
}

const formatStopOnWin = () => {
  const decimals = balanceStore.currentBalance?.decimals || 2
  stopOnWin.value = Math.max(0, parseFloat(stopOnWin.value.toFixed(decimals)))
}

const formatStopOnLoss = () => {
  const decimals = balanceStore.currentBalance?.decimals || 2
  stopOnLoss.value = Math.max(0, parseFloat(stopOnLoss.value.toFixed(decimals)))
}

// Auto-betting methods
const startAutoBet = async () => {
  if (isAutoBetting.value) return

  isAutoBetting.value = true
  sessionProfit.value = 0 // Reset session profit when starting autobet
  currentBetCount.value = 0 // Reset bet counter when starting autobet

  await performAutoBetSequence()
}

const stopAutoBet = () => {
  isAutoBetting.value = false
  sessionProfit.value = 0 // Reset session profit when stopping autobet
  currentBetCount.value = 0 // Reset bet counter when stopping autobet
}

const performAutoBetSequence = async () => {
  while (isAutoBetting.value) {
    // Check if we have enough balance
    if (betAmount.value > maxBet.value) {
      console.log('Auto-bet stopped: Insufficient balance')
      stopAutoBet()
      break
    }

    // Check if we've reached the maximum number of rolls (if not infinite)
    if (autoBetRolls.value > 0 && currentBetCount.value >= autoBetRolls.value) {
      console.log('Auto-bet stopped: Maximum rolls reached')
      stopAutoBet()
      break
    }

    try {
      await rollDice()
      currentBetCount.value++ // Increment bet counter

      // Update session profit with the last roll result
      if (diceStore.lastResult) {
        sessionProfit.value += diceStore.lastResult.profit

        // Check stop on win condition (if set and we have a positive session profit)
        if (stopOnWin.value > 0 && sessionProfit.value >= stopOnWin.value) {
          console.log(`Auto-bet stopped: Stop on win reached (${sessionProfit.value.toFixed(balanceStore.currentBalance?.decimals || 2)})`)
          stopAutoBet()
          break
        }

        // Check stop on loss condition (if set and we have a negative session profit)
        if (stopOnLoss.value > 0 && sessionProfit.value <= -stopOnLoss.value) {
          console.log(`Auto-bet stopped: Stop on loss reached (${sessionProfit.value.toFixed(balanceStore.currentBalance?.decimals || 2)})`)
          stopAutoBet()
          break
        }
      }

      // Small delay between rolls for better UX
      if (isAutoBetting.value) {
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    } catch (error) {
      console.error('Auto-bet roll failed:', error)
      stopAutoBet()
      break
    }
  }
}

// Watch for threshold changes to ensure valid range
watch(threshold, (newThreshold) => {
  const minThreshold = diceStore.config?.min_threshold || 2.00
  const maxThreshold = diceStore.config?.max_threshold || 98.00

  // Constrain threshold to valid range
  if (newThreshold < minThreshold) {
    threshold.value = minThreshold
  } else if (newThreshold > maxThreshold) {
    threshold.value = maxThreshold
  }
})

// Watch for direction changes to ensure valid win chance
watch(direction, () => {
  const minThreshold = diceStore.config?.min_threshold || 2.00
  const maxThreshold = diceStore.config?.max_threshold || 98.00

  if (winChance.value <= 0) {
    if (direction.value === 'over') {
      threshold.value = maxThreshold
    } else {
      threshold.value = minThreshold
    }
  }
})



const fetchMinimumBetInfo = async () => {
  if (!balanceStore.currentBalance) return

  try {
    // Get currency ID from the currencies API
    const currenciesResponse = await fetch(`${import.meta.env.VITE_API_URL}/api/currencies`)
    const currenciesData = await currenciesResponse.json()

    if (currenciesData.success) {
      const currency = currenciesData.data.find((c: any) => c.code === balanceStore.currentBalance?.currency_code)
      if (currency?.id) {
        await diceStore.getMinimumBet({ currency_id: currency.id })
      }
    }
  } catch (error) {
    console.error('Failed to fetch minimum bet info:', error)
  }
}

onMounted(async () => {
  // Initialize balance store if needed
  if (!balanceStore.isInitialized) {
    await balanceStore.fetchBalances()
  }

  // If still no current balance, try to initialize again
  if (!balanceStore.currentBalance && authStore.isAuthenticated) {
    await balanceStore.initialize()
  }

  // Fetch dice game config
  await diceStore.getConfig()

  // Fetch minimum bet info for current currency
  await fetchMinimumBetInfo()

  // Initialize dice store WebSocket
  diceStore.initialize()
})

// Watch for currency changes and update minimum bet info
watch(() => balanceStore.currentBalance?.currency_code, async () => {
  await fetchMinimumBetInfo()
})
</script>

<template>
  <div class="dice-game">
    <div class="game-container">
      <div class="game-content">
        <!-- Slider Section -->
        <div class="slider-section">
          <!-- Header with Direction Toggle and Recent Rolls -->
          <div class="slider-header">
            <div class="direction-toggle">
              <button
                @click="direction = 'over'"
                :class="{ active: direction === 'over' }"
                class="direction-btn"
                :disabled="isRolling || isAutoBetting"
              >
                Over
              </button>
              <button
                @click="direction = 'under'"
                :class="{ active: direction === 'under' }"
                class="direction-btn"
                :disabled="isRolling || isAutoBetting"
              >
                Under
              </button>
            </div>

            <!-- Recent Rolls in Top Right -->
            <div class="recent-rolls">
              <div class="roll-history">
                <div
                  v-for="roll in diceStore.gameHistory"
                  :key="roll.id"
                  class="history-item"
                  :class="{ 'win': roll.win, 'loss': !roll.win }"
                  :title="`Roll: ${roll.roll} | ${roll.win ? 'Won' : 'Lost'} | Profit: ${roll.profit_formatted}`"
                >
                  {{ roll.roll }}
                </div>
              </div>
            </div>
          </div>

          <!-- Slider Container -->
          <div class="slider-container">
            <input
              id="threshold"
              v-model.number="threshold"
              type="range"
              min="0"
              max="100"
              step="1"
              class="threshold-slider"
              :class="{ 'over-direction': direction === 'over', 'under-direction': direction === 'under' }"
              :style="{ '--threshold-percent': thresholdPercent }"
              :disabled="isRolling || isAutoBetting"
            />
            <!-- Dice Result Indicator -->
            <div
              v-if="diceStore.lastResult"
              class="dice-result-indicator"
              :class="{ 'win': diceStore.lastResult.win, 'loss': !diceStore.lastResult.win }"
              :style="{ left: `${diceStore.lastResult.roll}%` }"
            >
              <div class="dice-result-value">{{ diceStore.lastResult.roll }}</div>
            </div>
            <div class="slider-markers">
              <span>0</span>
              <span>25</span>
              <span>50</span>
              <span>75</span>
              <span>100</span>
            </div>
          </div>

          <!-- Stats Below Slider -->
          <div class="stats-section">
            <div class="stat-group">
              <label class="stat-label">Multiplier</label>
              <div class="stat-value">{{ multiplier.toFixed(2) }}x</div>
            </div>
            <div class="stat-group">
              <label class="stat-label">Roll {{ direction === 'over' ? 'Over' : 'Under' }}</label>
              <div class="stat-value">{{ threshold.toFixed(2) }}</div>
            </div>
            <div class="stat-group">
              <label class="stat-label">Win Chance</label>
              <div class="stat-value">{{ winChance.toFixed(2) }}%</div>
            </div>
          </div>
        </div>

      <!-- Controls Section Below Game -->
      <div class="controls-section">
        <div class="controls-content">
          <!-- Mode Toggle -->
          <div class="mode-toggle">
            <button
              @click="autoBetMode = 'manual'"
              :class="{ active: autoBetMode === 'manual' }"
              class="mode-btn"
              :disabled="isAutoBetting"
            >
              Manual
            </button>
            <button
              @click="autoBetMode = 'auto'"
              :class="{ active: autoBetMode === 'auto' }"
              class="mode-btn"
              :disabled="isAutoBetting"
            >
              Auto
            </button>
          </div>

          <div class="controls-row">
            <!-- Bet Amount -->
            <div class="control-group">
              <label class="section-label">Bet Amount</label>
              <div class="bet-amount-row">
                <div class="bet-amount-input-container">
                  <input
                    v-model.number="betAmount"
                    type="number"
                    :min="minBet"
                    :max="maxBet"
                    :step="betStep"
                    class="bet-amount-input"
                    :disabled="isRolling || isAutoBetting"
                    @blur="formatBetAmount"
                    @input="formatBetAmount"
                  />
                  <span class="currency-symbol">{{ balanceStore.currentBalance?.currency_code || 'USD' }}</span>
                </div>
                <div class="bet-controls">
                  <button @click="setMinBet" class="bet-control-btn" :disabled="isRolling || isAutoBetting">
                    Min
                  </button>
                  <button @click="halveBet" class="bet-control-btn" :disabled="isRolling || isAutoBetting">
                    <span class="bet-icon">½</span>
                  </button>
                  <button @click="doubleBet" class="bet-control-btn" :disabled="isRolling || isAutoBetting">
                    <span class="bet-icon">2x</span>
                  </button>
                  <button @click="setMaxBet" class="bet-control-btn" :disabled="isRolling || isAutoBetting">
                    Max
                  </button>
                </div>
              </div>
            </div>

            <!-- Profit on Win -->
            <div class="control-group">
              <label class="section-label">Profit on Win</label>
              <div class="profit-display">
                {{ estimatedProfit.toFixed(balanceStore.currentBalance?.decimals || 2) }} {{ balanceStore.currentBalance?.currency_code || 'USD' }}
              </div>
            </div>

            <!-- Auto Mode Settings (only show in auto mode) -->
            <div v-if="autoBetMode === 'auto'" class="control-group">
              <label class="section-label">
                Rolls (0 = ∞)
                <span v-if="isAutoBetting" class="roll-counter">
                  - {{ currentBetCount }}/{{ autoBetRolls === 0 ? '∞' : autoBetRolls }}
                </span>
              </label>
              <input
                v-model.number="autoBetRolls"
                type="number"
                min="0"
                max="1000"
                step="1"
                class="auto-rolls-input"
                :disabled="isAutoBetting"
                @blur="formatAutoBetRolls"
                @input="formatAutoBetRolls"
              />
            </div>

            <div v-if="autoBetMode === 'auto'" class="control-group">
              <label class="section-label">Stop on Win</label>
              <div class="stop-input-container">
                <input
                  v-model.number="stopOnWin"
                  type="number"
                  min="0"
                  :step="betStep"
                  class="stop-input"
                  :disabled="isAutoBetting"
                  @blur="formatStopOnWin"
                  @input="formatStopOnWin"
                  placeholder="0 = disabled"
                />
                <span class="currency-symbol">{{ balanceStore.currentBalance?.currency_code || 'USD' }}</span>
              </div>
            </div>

            <div v-if="autoBetMode === 'auto'" class="control-group">
              <label class="section-label">Stop on Loss</label>
              <div class="stop-input-container">
                <input
                  v-model.number="stopOnLoss"
                  type="number"
                  min="0"
                  :step="betStep"
                  class="stop-input"
                  :disabled="isAutoBetting"
                  @blur="formatStopOnLoss"
                  @input="formatStopOnLoss"
                  placeholder="0 = disabled"
                />
                <span class="currency-symbol">{{ balanceStore.currentBalance?.currency_code || 'USD' }}</span>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="action-buttons">
            <!-- Manual Mode Button -->
            <button
              v-if="autoBetMode === 'manual'"
              @click="rollDice"
              class="bet-button"
              :disabled="isRolling || !canBet"
              :class="{ rolling: isRolling }"
            >
              {{ isRolling ? 'Rolling...' : 'Bet' }}
            </button>

            <!-- Auto Mode Buttons -->
            <template v-else>
              <button
                v-if="isAutoBetting"
                @click="stopAutoBet"
                class="stop-button"
              >
                Stop Auto Bet
              </button>

              <button
                v-else
                @click="startAutoBet"
                class="bet-button auto-bet-button"
                :disabled="isRolling || !canBet"
              >
                Start Auto Bet
              </button>
            </template>
          </div>
        </div>
      </div>

      </div>
    </div>
  </div>
</template>

<style scoped>
.dice-game {
  max-width: 800px;
  margin: 0 auto;
  padding: 0.5rem;
}

.game-container {
  background: transparent;
  color: #1f2937;
}

.game-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Controls Section */
.controls-section {

}

.controls-content {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  align-items: left;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.controls-content .mode-toggle {
  margin-bottom: 1rem;
}

.controls-row {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
  width: 100%;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 0.15rem;
  align-items: flex-start;
  width: 100%;
}

.action-buttons {
  display: flex;
  justify-content: left;
  gap: 1rem;
  width: 100%;
  padding-left: 0.5rem;
}

/* Mode Toggle */
.mode-toggle {
  display: inline-flex;
  background: #e9ecef;
  border-radius: 4px;
  padding: 2px;
  width: fit-content;
  gap: 1px;
}

.mode-btn {
  padding: 0.25rem 0.6rem;
  border: none;
  background: transparent;
  color: #6c757d;
  border-radius: 3px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.75rem;
  transition: all 0.2s;
}

.mode-btn.active {
  background: #42b883;
  color: white;
  box-shadow: 0 1px 2px rgba(66, 184, 131, 0.3);
}

.mode-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}





.section-label {
  display: block;
  color: #495057;
  font-size: 0.75rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.bet-amount-row {
  display: flex;
  gap: 0.5rem;
  align-items: flex-end;
}

.bet-amount-input-container {
  position: relative;
  flex: 1;
}

.bet-amount-input {
  width: 100%;
  padding: 0.4rem 2.5rem 0.4rem 0.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  background: white;
  color: #495057;
  outline: none;
  transition: border-color 0.2s;
}

.bet-amount-input:focus {
  border-color: #42b883;
}

.bet-amount-input:disabled {
  background: #f8f9fa;
  opacity: 0.7;
}

.currency-symbol {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.75rem;
  font-weight: 500;
  color: #6c757d;
  pointer-events: none;
}

.bet-controls {
  display: flex;
  gap: 0.25rem;
}

.bet-control-btn {
  padding: 0.25rem 0.4rem;
  background: #e9ecef;
  border: none;
  border-radius: 3px;
  color: #6c757d;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.65rem;
  transition: all 0.2s;
  width: 40px;
  text-align: center;
}

.bet-control-btn:hover:not(:disabled) {
  background: rgba(66, 184, 131, 0.1);
  color: #42b883;
}

.bet-control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.bet-icon {
  font-size: 0.8rem;
  font-weight: bold;
}



.profit-display {
  font-size: 0.9rem;
  font-weight: 600;
  color: #495057;
}

.bet-button {
  padding: 0.5rem 1rem;
  background: #42b883;
  border: none;
  border-radius: 4px;
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  width: auto;
  min-width: 120px;
}

.bet-button:hover:not(:disabled) {
  background: #369870;
}

.bet-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.bet-button.rolling {
  background: #f59e0b;
}

/* Auto-bet Controls */
.auto-controls {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}



.auto-rolls-input {
  width: 100%;
  padding: 0.4rem 0.5rem;
  font-size: 0.8rem;
  font-weight: 600;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  background: white;
  color: #495057;
  outline: none;
  transition: border-color 0.2s;
}

.auto-rolls-input:focus {
  border-color: #42b883;
}

.auto-rolls-input:disabled {
  background: #f8f9fa;
  opacity: 0.7;
}

.stop-input-container {
  position: relative;
  width: 100%;
}

.stop-input {
  width: 100%;
  padding: 0.4rem 2.5rem 0.4rem 0.5rem;
  font-size: 0.8rem;
  font-weight: 600;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  background: white;
  color: #495057;
  outline: none;
  transition: border-color 0.2s;
}

.stop-input:focus {
  border-color: #42b883;
}

.stop-input:disabled {
  background: #f8f9fa;
  opacity: 0.7;
}

.roll-counter {
  color: #6c757d;
  font-weight: 500;
  font-size: 0.7rem;
}

.stop-button {
  padding: 0.5rem 1rem;
  background: #dc2626;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  width: auto;
  min-width: 120px;
}

.stop-button:hover {
  background: #b91c1c;
}

.auto-bet-button {
  background: #f59e0b;
}

.auto-bet-button:hover:not(:disabled) {
  background: #d97706;
}





.direction-toggle {
  display: inline-flex;
  background: #e9ecef;
  border-radius: 4px;
  padding: 2px;
  gap: 1px;
  width: fit-content;
}

.direction-btn {
  flex: 0 0 auto;
  padding: 0.25rem 0.6rem;
  border: none;
  background: transparent;
  color: #6c757d;
  border-radius: 3px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.75rem;
  transition: all 0.2s;
}

.direction-btn.active {
  background: #42b883;
  color: white;
  box-shadow: 0 1px 2px rgba(66, 184, 131, 0.3);
}

.direction-btn:hover:not(:disabled):not(.active) {
  background: rgba(66, 184, 131, 0.1);
  color: #42b883;
}

.direction-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Slider Section */
.slider-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
}

.slider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 2rem;
}

.recent-rolls {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  margin-left: 1rem;
}

.recent-rolls .roll-history {
  display: flex;
  gap: 0.25rem;
  max-width: 300px;
  overflow: hidden;
  justify-content: flex-end;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-top: 1.5rem;
  padding-top: 0.75rem;
  border-top: 1px solid #dee2e6;
}

.stat-group {
  text-align: center;
}

.stat-label {
  display: block;
  color: #6c757d;
  font-size: 0.7rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.stat-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: #495057;
}

.slider-container {
  position: relative;
  margin: 2rem 0 0.5rem 0;
}

/* Dice Result Indicator */
.dice-result-indicator {
  position: absolute;
  top: -15px;
  width: 3px;
  height: 50px;
  transform: translateX(-50%);
  z-index: 3;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.dice-result-indicator.win {
  background: #22c55e;
  box-shadow: 0 0 10px rgba(34, 197, 94, 0.5);
}

.dice-result-indicator.loss {
  background: #dc2626;
  box-shadow: 0 0 10px rgba(220, 38, 38, 0.5);
}

.dice-result-value {
  position: absolute;
  top: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border: 2px solid;
  border-radius: 8px;
  padding: 4px 8px;
  font-size: 0.875rem;
  font-weight: bold;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dice-result-indicator.win .dice-result-value {
  border-color: #22c55e;
  color: #22c55e;
}

.dice-result-indicator.loss .dice-result-value {
  border-color: #dc2626;
  color: #dc2626;
}

.threshold-slider {
  width: 100%;
  height: 12px;
  border-radius: 6px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
  background: #e9ecef;
  position: relative;
  z-index: 1;
}

.threshold-slider::-webkit-slider-track {
  height: 12px;
  border-radius: 6px;
  background: #e9ecef;
}

.threshold-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 4px;
  background: #42b883;
  cursor: pointer;
  border: 1px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 2;
}

.threshold-slider::-moz-range-track {
  height: 12px;
  border-radius: 6px;
  border: none;
  background: #e9ecef;
}

.threshold-slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 4px;
  background: #42b883;
  cursor: pointer;
  border: 1px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  border: none;
}

.slider-markers {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5rem;
  padding: 0;
  font-size: 0.7rem;
  font-weight: 500;
  color: #6c757d;
  position: relative;
  margin-left: 15px;
  margin-right: 15px;
}

.slider-markers span {
  position: absolute;
  transform: translateX(-50%);
}

.slider-markers span:nth-child(1) { left: 0%; }
.slider-markers span:nth-child(2) { left: 25%; }
.slider-markers span:nth-child(3) { left: 50%; }
.slider-markers span:nth-child(4) { left: 75%; }
.slider-markers span:nth-child(5) { left: 100%; }



.roll-history {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.history-item {
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-size: 0.7rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 30px;
  text-align: center;
  background: #e9ecef;
  color: #6c757d;
  animation: slideInRight 0.3s ease-out;
}

.history-item.win {
  background: #42b883;
  color: white;
}

.history-item.loss {
  background: #6c757d;
  color: white;
}

.history-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Over direction: green on right (winning side), red on left (losing side) */
.threshold-slider.over-direction {
  background: linear-gradient(to right, #dc2626 0%, #dc2626 var(--threshold-percent), #22c55e var(--threshold-percent), #22c55e 100%);
}

.threshold-slider.over-direction::-webkit-slider-track {
  background: linear-gradient(to right, #dc2626 0%, #dc2626 var(--threshold-percent), #22c55e var(--threshold-percent), #22c55e 100%);
}

.threshold-slider.over-direction::-moz-range-track {
  background: linear-gradient(to right, #dc2626 0%, #dc2626 var(--threshold-percent), #22c55e var(--threshold-percent), #22c55e 100%);
}

/* Under direction: green on left (winning side), red on right (losing side) */
.threshold-slider.under-direction {
  background: linear-gradient(to right, #22c55e 0%, #22c55e var(--threshold-percent), #dc2626 var(--threshold-percent), #dc2626 100%);
}

.threshold-slider.under-direction::-webkit-slider-track {
  background: linear-gradient(to right, #22c55e 0%, #22c55e var(--threshold-percent), #dc2626 var(--threshold-percent), #dc2626 100%);
}

.threshold-slider.under-direction::-moz-range-track {
  background: linear-gradient(to right, #22c55e 0%, #22c55e var(--threshold-percent), #dc2626 var(--threshold-percent), #dc2626 100%);
}





/* Responsive Design */
@media (max-width: 1024px) {
  .game-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .mode-toggle {
    justify-self: center;
  }
}

@media (max-width: 768px) {
  .dice-game {
    padding: 1rem;
  }

  .game-container {
    padding: 1rem;
  }

  .bottom-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .settings-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}
</style>
