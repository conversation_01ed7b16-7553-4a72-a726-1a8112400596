<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useBalanceStore } from '@/stores/balance'
import { useCoinflipStore } from '@/stores/coinflip'

const authStore = useAuthStore()
const balanceStore = useBalanceStore()
const coinflipStore = useCoinflipStore()

// Game creation state
const betAmount = ref<number>(1.00)
const selectedSide = ref<'heads' | 'tails'>('heads')
const isCreating = ref<boolean>(false)

// Animation state - track per game
const gameAnimations = ref<Map<number, { isFlipping: boolean, result: 'heads' | 'tails' | null }>>(new Map())
const isFlipping = ref<boolean>(false)
const coinResult = ref<'heads' | 'tails' | null>(null)

// Computed properties
const maxBet = computed(() => {
  if (!balanceStore.currentBalance) return 0
  return balanceStore.currentBalance.formatted
})

const minBet = computed(() => {
  return coinflipStore.minimumBetInfo?.minimum_bet || 0.01
})

const betStep = computed(() => {
  const decimals = balanceStore.currentBalance?.decimals || 2
  return 1 / Math.pow(10, decimals)
})

const canCreateGame = computed(() => {
  return betAmount.value >= minBet.value &&
         betAmount.value <= maxBet.value &&
         !isCreating.value &&
         !isFlipping.value
})

// Bet shortcuts
const setBetAmount = (amount: number) => {
  betAmount.value = Math.max(minBet.value, Math.min(amount, maxBet.value))
}

const setBetMin = () => setBetAmount(minBet.value)
const setBetHalf = () => setBetAmount(betAmount.value / 2)
const setBetDouble = () => setBetAmount(betAmount.value * 2)
const setBetMax = () => setBetAmount(maxBet.value)

// Methods
const createGame = async () => {
  if (!canCreateGame.value) return

  isCreating.value = true

  try {
    // Get currency ID from the currencies API
    let currencyId = null
    if (balanceStore.currentBalance) {
      const currenciesResponse = await fetch(`${import.meta.env.VITE_API_URL}/api/currencies`)
      const currenciesData = await currenciesResponse.json()

      if (currenciesData.success) {
        const currency = currenciesData.data.find((c: any) => c.code === balanceStore.currentBalance?.currency_code)
        currencyId = currency?.id
      }
    }

    await coinflipStore.createGame({
      bet: betAmount.value,
      side: selectedSide.value,
      currency_id: currencyId
    })

    // Don't reset bet amount - keep user's preference
  } catch (error) {
    console.error('Create game failed:', error)
  } finally {
    isCreating.value = false
  }
}

const joinGame = async (gameId: number) => {
  if (gameAnimations.value.get(gameId)?.isFlipping) return

  // Set animation state for this specific game
  gameAnimations.value.set(gameId, { isFlipping: true, result: null })

  try {
    const result = await coinflipStore.joinGame(gameId)

    // Trigger coin flip animation for this game
    await animateCoinFlipForGame(gameId, result.result!)
  } catch (error) {
    console.error('Join game failed:', error)
    gameAnimations.value.set(gameId, { isFlipping: false, result: null })
  }
}

const joinWithBot = async (gameId: number) => {
  if (gameAnimations.value.get(gameId)?.isFlipping) return

  // Set animation state for this specific game
  gameAnimations.value.set(gameId, { isFlipping: true, result: null })

  try {
    const result = await coinflipStore.joinWithBot(gameId)

    // Trigger coin flip animation for this game
    await animateCoinFlipForGame(gameId, result.result!)
  } catch (error) {
    console.error('Join with bot failed:', error)
    gameAnimations.value.set(gameId, { isFlipping: false, result: null })
  }
}

const animateCoinFlip = async (result: 'heads' | 'tails'): Promise<void> => {
  return new Promise((resolve) => {
    // Start animation
    coinResult.value = null

    // Simulate coin flip animation duration
    setTimeout(() => {
      coinResult.value = result
      isFlipping.value = false
      resolve()
    }, 2000) // 2 second animation
  })
}

const animateCoinFlipForGame = async (gameId: number, result: 'heads' | 'tails'): Promise<void> => {
  return new Promise((resolve) => {
    // Start animation for specific game
    gameAnimations.value.set(gameId, { isFlipping: true, result: null })

    // Simulate coin flip animation duration
    setTimeout(() => {
      gameAnimations.value.set(gameId, { isFlipping: false, result })

      // Clear animation after showing result for a bit
      setTimeout(() => {
        gameAnimations.value.delete(gameId)
      }, 3000) // Show result for 3 seconds

      resolve()
    }, 2000) // 2 second animation
  })
}

const clearCompletedGames = () => {
  // Filter out completed games, keep only waiting games
  coinflipStore.myGames = coinflipStore.myGames.filter(game => game.status === 'waiting')
}

const fetchMinimumBetInfo = async () => {
  if (!balanceStore.currentBalance) return

  try {
    const currenciesResponse = await fetch(`${import.meta.env.VITE_API_URL}/api/currencies`)
    const currenciesData = await currenciesResponse.json()

    if (currenciesData.success) {
      const currency = currenciesData.data.find((c: any) => c.code === balanceStore.currentBalance?.currency_code)
      if (currency) {
        await coinflipStore.getMinimumBet(currency.id)
      }
    }
  } catch (error) {
    console.error('Failed to fetch minimum bet info:', error)
  }
}

const refreshLobby = () => {
  coinflipStore.fetchLobby()
  coinflipStore.fetchMyGames()
}

// Handle WebSocket game resolution events
const handleGameResolved = (event: CustomEvent) => {
  const { gameId, result } = event.detail
  if (result) {
    animateCoinFlipForGame(gameId, result)
  }
}

// Lifecycle
onMounted(async () => {
  // Initialize balance store if needed
  if (!balanceStore.isInitialized) {
    await balanceStore.fetchBalances()
  }

  // If still no current balance, try to initialize again
  if (!balanceStore.currentBalance && authStore.isAuthenticated) {
    await balanceStore.initialize()
  }

  // Fetch coinflip game config
  await coinflipStore.getConfig()

  // Fetch minimum bet info for current currency
  await fetchMinimumBetInfo()

  // Initialize coinflip store WebSocket
  coinflipStore.initialize()

  // Listen for game resolution events
  window.addEventListener('coinflip-game-resolved', handleGameResolved as EventListener)
})

onUnmounted(() => {
  coinflipStore.cleanupWebSocket()
  window.removeEventListener('coinflip-game-resolved', handleGameResolved as EventListener)
})

// Watch for currency changes and update minimum bet info
watch(() => balanceStore.currentBalance?.currency_code, async () => {
  await fetchMinimumBetInfo()
})

// Auto-refresh lobby every 30 seconds
const refreshInterval = setInterval(refreshLobby, 30000)
onUnmounted(() => {
  clearInterval(refreshInterval)
})
</script>

<template>
  <div class="coinflip-game">
    <div class="game-container">
      <!-- Game Creation Section -->
      <div class="game-creation">
        <h2>Create Coinflip Game</h2>

        <div class="creation-form">
          <!-- Bet Amount Input -->
          <div class="input-group">
            <label for="bet-amount">Bet Amount</label>
            <div class="input-wrapper">
              <input
                id="bet-amount"
                v-model.number="betAmount"
                type="number"
                :min="minBet"
                :max="maxBet"
                :step="betStep"
                class="bet-input"
                :disabled="isCreating || isFlipping"
              />
              <span class="currency-symbol">{{ balanceStore.currentBalance?.symbol || '$' }}</span>
            </div>

            <!-- Bet Shortcuts -->
            <div class="bet-shortcuts">
              <button @click="setBetMin" class="shortcut-btn" :disabled="isCreating || isFlipping">Min</button>
              <button @click="setBetHalf" class="shortcut-btn" :disabled="isCreating || isFlipping">1/2</button>
              <button @click="setBetDouble" class="shortcut-btn" :disabled="isCreating || isFlipping">2x</button>
              <button @click="setBetMax" class="shortcut-btn" :disabled="isCreating || isFlipping">Max</button>
            </div>
          </div>

          <!-- Side Selection -->
          <div class="input-group">
            <label>Choose Side</label>
            <div class="side-selector">
              <button
                @click="selectedSide = 'heads'"
                :class="{ active: selectedSide === 'heads' }"
                class="side-btn"
                :disabled="isCreating || isFlipping"
              >
                <div class="coin-icon heads">H</div>
                Heads
              </button>
              <button
                @click="selectedSide = 'tails'"
                :class="{ active: selectedSide === 'tails' }"
                class="side-btn"
                :disabled="isCreating || isFlipping"
              >
                <div class="coin-icon tails">T</div>
                Tails
              </button>
            </div>
          </div>

          <!-- Create Game Button -->
          <button
            @click="createGame"
            class="create-btn"
            :disabled="!canCreateGame"
            :class="{ creating: isCreating }"
          >
            {{ isCreating ? 'Creating...' : 'Create Game' }}
          </button>
        </div>
      </div>



      <!-- Game Lobby -->
      <div class="game-lobby">
        <div class="lobby-section">
          <div class="section-header">
            <h3>My Games</h3>
            <div class="header-actions">
              <button @click="clearCompletedGames()" class="clear-btn" title="Clear completed games">🗑️</button>
              <button @click="coinflipStore.fetchMyGames()" class="refresh-btn">↻</button>
            </div>
          </div>

          <div class="games-list">
            <div v-if="coinflipStore.myGames.length === 0" class="no-games">
              No games yet
            </div>

            <div
              v-for="game in coinflipStore.myGames"
              :key="game.id"
              class="game-card my-game"
              :class="{
                waiting: game.status === 'waiting',
                completed: game.status === 'completed',
                won: game.winner?.id === authStore.user?.id,
                lost: game.status === 'completed' && game.winner?.id !== authStore.user?.id
              }"
            >
              <div class="game-info">
                <div class="game-amount">{{ game.amount_formatted }}</div>
                <div class="game-sides">
                  <span class="creator-side" :class="{ you: game.creator?.id === authStore.user?.id }">
                    {{ game.creator_side.toUpperCase() }}
                    {{ game.creator?.id === authStore.user?.id ? ' (You)' : ` (${game.creator?.username})` }}
                  </span>
                  <span class="vs">vs</span>
                  <span v-if="game.joiner_side" class="joiner-side" :class="{ you: game.joiner?.id === authStore.user?.id }">
                    {{ game.joiner_side.toUpperCase() }}
                    {{ game.joiner?.id === authStore.user?.id ? ' (You)' : ` (${game.joiner?.username})` }}
                  </span>
                  <span v-else class="waiting-text">Waiting...</span>
                </div>

                <div v-if="game.status === 'completed'" class="game-result">
                  Result: {{ game.result?.toUpperCase() }}
                  <span v-if="game.winner" class="winner">
                    Winner: {{ game.winner.id === authStore.user?.id ? 'You' : game.winner.username }}
                  </span>
                </div>
              </div>

              <!-- Inline Coin Animation -->
              <div v-if="gameAnimations.get(game.id)" class="inline-coin-animation">
                <div class="coin-container">
                  <div
                    class="coin"
                    :class="{
                      flipping: gameAnimations.get(game.id)?.isFlipping,
                      heads: gameAnimations.get(game.id)?.result === 'heads',
                      tails: gameAnimations.get(game.id)?.result === 'tails'
                    }"
                  >
                    <div class="coin-side heads-side">H</div>
                    <div class="coin-side tails-side">T</div>
                  </div>
                </div>

                <div v-if="gameAnimations.get(game.id)?.result" class="result-info">
                  <div class="result-text">{{ gameAnimations.get(game.id)?.result?.toUpperCase() }}!</div>
                  <div v-if="game.winner?.id === authStore.user?.id" class="win-message">You won!</div>
                  <div v-else class="lose-message">You lost</div>
                </div>
              </div>

              <div class="game-actions">
                <button
                  v-if="game.status === 'waiting' && game.creator?.id === authStore.user?.id"
                  @click="joinWithBot(game.id)"
                  class="bot-btn"
                  :disabled="gameAnimations.get(game.id)?.isFlipping"
                >
                  Call Bot
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="lobby-section">
          <div class="section-header">
            <h3>Joinable Games</h3>
            <button @click="coinflipStore.fetchLobby()" class="refresh-btn">↻</button>
          </div>

          <div class="games-list">
            <div v-if="coinflipStore.waitingGames.length === 0" class="no-games">
              No games available
            </div>

            <div
              v-for="game in coinflipStore.waitingGames"
              :key="game.id"
              class="game-card joinable-game"
            >
              <div class="game-info">
                <div class="game-amount">{{ game.amount_formatted }}</div>
                <div class="game-creator">
                  {{ game.creator?.username }} chose {{ game.creator_side.toUpperCase() }}
                </div>
                <div class="your-side">
                  You get {{ game.creator_side === 'heads' ? 'TAILS' : 'HEADS' }}
                </div>
              </div>

              <!-- Inline Coin Animation -->
              <div v-if="gameAnimations.get(game.id)" class="inline-coin-animation">
                <div class="coin-container">
                  <div
                    class="coin"
                    :class="{
                      flipping: gameAnimations.get(game.id)?.isFlipping,
                      heads: gameAnimations.get(game.id)?.result === 'heads',
                      tails: gameAnimations.get(game.id)?.result === 'tails'
                    }"
                  >
                    <div class="coin-side heads-side">H</div>
                    <div class="coin-side tails-side">T</div>
                  </div>
                </div>

                <div v-if="gameAnimations.get(game.id)?.result" class="result-info">
                  <div class="result-text">{{ gameAnimations.get(game.id)?.result?.toUpperCase() }}!</div>
                  <div v-if="game.winner?.id === authStore.user?.id" class="win-message">You won!</div>
                  <div v-else class="lose-message">You lost</div>
                </div>
              </div>

              <div class="game-actions">
                <button
                  @click="joinGame(game.id)"
                  class="join-btn"
                  :disabled="gameAnimations.get(game.id)?.isFlipping"
                >
                  Join Game
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.coinflip-game {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.game-container {
  background: transparent;
  color: #1f2937;
}

/* Game Creation Section */
.game-creation {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.game-creation h2 {
  margin: 0 0 1.5rem 0;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
}

.creation-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.input-group label {
  font-weight: 500;
  color: #374151;
}

.input-wrapper {
  position: relative;
  display: inline-block;
  width: 200px;
}

.bet-input {
  width: 100%;
  padding: 0.75rem;
  padding-right: 2.5rem;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.bet-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.bet-input:disabled {
  background-color: #f9fafb;
  color: #6b7280;
}

.currency-symbol {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  font-weight: 500;
}

/* Side Selector */
.side-selector {
  display: flex;
  gap: 1rem;
}

.side-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
}

.side-btn:hover:not(:disabled) {
  border-color: #3b82f6;
  background-color: #f8fafc;
}

.side-btn.active {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.side-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.coin-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
  color: white;
}

.coin-icon.heads {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

.coin-icon.tails {
  background: linear-gradient(135deg, #6b7280, #4b5563);
}

/* Bet Shortcuts */
.bet-shortcuts {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.shortcut-btn {
  padding: 0.5rem 0.75rem;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.shortcut-btn:hover:not(:disabled) {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.shortcut-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Create Button */
.create-btn {
  padding: 0.75rem 2rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  align-self: flex-start;
}

.create-btn:hover:not(:disabled) {
  background: #2563eb;
}

.create-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.create-btn.creating {
  background: #6b7280;
}

/* Coin Animation */
.coin-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  padding: 3rem;
  background: white;
  border-radius: 8px;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.coin-container {
  perspective: 1000px;
}

.coin {
  width: 120px;
  height: 120px;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

.coin.flipping {
  animation: coinFlip 2s ease-in-out;
}

.coin-side {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  font-weight: bold;
  color: white;
  backface-visibility: hidden;
}

.heads-side {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

.tails-side {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  transform: rotateY(180deg);
}

.coin.heads {
  transform: rotateY(0deg);
}

.coin.tails {
  transform: rotateY(180deg);
}

@keyframes coinFlip {
  0% { transform: rotateY(0deg); }
  50% { transform: rotateY(1800deg); }
  100% { transform: rotateY(1800deg); }
}

.result-info {
  text-align: center;
}

.result-info h3 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  color: #1f2937;
}

.win-message {
  color: #059669;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.lose-message {
  color: #dc2626;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

/* Game Lobby */
.game-lobby {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.lobby-section {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.refresh-btn,
.clear-btn {
  padding: 0.5rem;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1.2rem;
  transition: background-color 0.2s ease;
}

.refresh-btn:hover,
.clear-btn:hover {
  background: #e5e7eb;
}

.clear-btn {
  color: #dc2626;
}

.clear-btn:hover {
  background: #fef2f2;
  border-color: #fca5a5;
}

.games-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
}

.no-games {
  text-align: center;
  color: #6b7280;
  padding: 2rem;
  font-style: italic;
}

.game-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  transition: all 0.2s ease;
  position: relative;
}

.game-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Inline Coin Animation */
.inline-coin-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.inline-coin-animation .coin-container {
  perspective: 1000px;
}

.inline-coin-animation .coin {
  width: 60px;
  height: 60px;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

.inline-coin-animation .coin.flipping {
  animation: coinFlip 2s ease-in-out;
}

.inline-coin-animation .coin-side {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  backface-visibility: hidden;
}

.inline-coin-animation .heads-side {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

.inline-coin-animation .tails-side {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  transform: rotateY(180deg);
}

.inline-coin-animation .coin.heads {
  transform: rotateY(0deg);
}

.inline-coin-animation .coin.tails {
  transform: rotateY(180deg);
}

.inline-coin-animation .result-info {
  text-align: center;
}

.inline-coin-animation .result-text {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.inline-coin-animation .win-message {
  color: #059669;
  font-size: 0.875rem;
  font-weight: 500;
}

.inline-coin-animation .lose-message {
  color: #dc2626;
  font-size: 0.875rem;
  font-weight: 500;
}

.my-game.waiting {
  border-left: 4px solid #f59e0b;
}

.my-game.won {
  border-left: 4px solid #059669;
  background-color: #f0fdf4;
}

.my-game.lost {
  border-left: 4px solid #dc2626;
  background-color: #fef2f2;
}

.game-info {
  flex: 1;
}

.game-amount {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.game-sides {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.creator-side.you,
.joiner-side.you {
  color: #3b82f6;
  font-weight: 500;
}

.vs {
  font-weight: bold;
  color: #9ca3af;
}

.waiting-text {
  color: #f59e0b;
  font-style: italic;
}

.game-result {
  font-size: 0.85rem;
  color: #4b5563;
}

.winner {
  margin-left: 0.5rem;
  font-weight: 500;
}

.game-creator {
  font-size: 0.9rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.your-side {
  font-size: 0.9rem;
  color: #3b82f6;
  font-weight: 500;
}

/* Action Buttons */
.game-actions {
  display: flex;
  gap: 0.5rem;
}

.join-btn,
.bot-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.join-btn {
  background: #3b82f6;
  color: white;
}

.join-btn:hover:not(:disabled) {
  background: #2563eb;
}

.bot-btn {
  background: #f59e0b;
  color: white;
}

.bot-btn:hover:not(:disabled) {
  background: #d97706;
}

.join-btn:disabled,
.bot-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .game-lobby {
    grid-template-columns: 1fr;
  }

  .side-selector {
    justify-content: center;
  }

  .coin-animation {
    padding: 2rem 1rem;
  }

  .coin {
    width: 100px;
    height: 100px;
  }

  .coin-side {
    font-size: 2.5rem;
  }
}
</style>
