import { ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import websocketService from '@/services/websocket'
import toastService, { type ToastNotification } from '@/services/toast'

export function useToastNotifications() {
  const authStore = useAuthStore()
  const isListening = ref(false)

  // Toast notification callback reference for proper cleanup
  const toastNotificationCallback = (e: ToastNotification) => {
    console.log('Toast Notifications: Received notification:', e)
    toastService.handleNotification(e)
  }

  const startListening = () => {
    if (!authStore.isAuthenticated || !authStore.user || isListening.value) {
      console.log('Toast Notifications: Cannot start listening - auth:', authStore.isAuthenticated, 'user:', !!authStore.user, 'already listening:', isListening.value)
      return
    }

    console.log('Toast Notifications: Setting up private channel listener for user', authStore.user.id)

    try {
      // Subscribe to toast notifications through the centralized WebSocket service
      websocketService.subscribe('toast.notification', toastNotificationCallback)
      isListening.value = true
      console.log('Toast Notifications: Successfully subscribed to toast notifications')
    } catch (error) {
      console.error('Toast Notifications: Failed to subscribe to notifications:', error)
      isListening.value = false
    }
  }

  const stopListening = () => {
    if (isListening.value) {
      console.log('Toast Notifications: Unsubscribing from toast notifications')
      try {
        websocketService.unsubscribe('toast.notification', toastNotificationCallback)
        isListening.value = false
      } catch (error) {
        console.error('Toast Notifications: Failed to unsubscribe:', error)
      }
    }
  }

  const reconnect = () => {
    stopListening()
    setTimeout(() => {
      startListening()
    }, 1000)
  }

  return {
    isListening,
    startListening,
    stopListening,
    reconnect
  }
}
