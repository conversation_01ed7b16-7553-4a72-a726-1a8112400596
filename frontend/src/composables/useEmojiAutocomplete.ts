import { ref, computed } from 'vue'

export interface EmojiData {
  emoji: string
  name: string
  shortcodes: string[]
}

// Extended emoji database for autocomplete
export const emojiDatabase: EmojiData[] = [
  { emoji: '😀', name: 'grinning face', shortcodes: ['grinning'] },
  { emoji: '😃', name: 'grinning face with big eyes', shortcodes: ['smiley'] },
  { emoji: '😄', name: 'grinning face with smiling eyes', shortcodes: ['smile'] },
  { emoji: '😁', name: 'beaming face with smiling eyes', shortcodes: ['grin'] },
  { emoji: '😆', name: 'grinning squinting face', shortcodes: ['laughing', 'satisfied'] },
  { emoji: '😅', name: 'grinning face with sweat', shortcodes: ['sweat_smile'] },
  { emoji: '🤣', name: 'rolling on the floor laughing', shortcodes: ['rofl'] },
  { emoji: '😂', name: 'face with tears of joy', shortcodes: ['joy'] },
  { emoji: '🙂', name: 'slightly smiling face', shortcodes: ['slightly_smiling_face'] },
  { emoji: '🙃', name: 'upside-down face', shortcodes: ['upside_down_face'] },
  { emoji: '😉', name: 'winking face', shortcodes: ['wink'] },
  { emoji: '😊', name: 'smiling face with smiling eyes', shortcodes: ['blush'] },
  { emoji: '😇', name: 'smiling face with halo', shortcodes: ['innocent'] },
  { emoji: '🥰', name: 'smiling face with hearts', shortcodes: ['smiling_face_with_hearts'] },
  { emoji: '😍', name: 'smiling face with heart-eyes', shortcodes: ['heart_eyes'] },
  { emoji: '🤩', name: 'star-struck', shortcodes: ['star_struck'] },
  { emoji: '😘', name: 'face blowing a kiss', shortcodes: ['kissing_heart'] },
  { emoji: '😗', name: 'kissing face', shortcodes: ['kissing'] },
  { emoji: '☺️', name: 'smiling face', shortcodes: ['relaxed'] },
  { emoji: '😚', name: 'kissing face with closed eyes', shortcodes: ['kissing_closed_eyes'] },
  { emoji: '😙', name: 'kissing face with smiling eyes', shortcodes: ['kissing_smiling_eyes'] },
  { emoji: '🥲', name: 'smiling face with tear', shortcodes: ['smiling_face_with_tear'] },
  { emoji: '😋', name: 'face savoring food', shortcodes: ['yum'] },
  { emoji: '😛', name: 'face with tongue', shortcodes: ['stuck_out_tongue'] },
  { emoji: '😜', name: 'winking face with tongue', shortcodes: ['stuck_out_tongue_winking_eye'] },
  { emoji: '🤪', name: 'zany face', shortcodes: ['zany_face'] },
  { emoji: '😝', name: 'squinting face with tongue', shortcodes: ['stuck_out_tongue_closed_eyes'] },
  { emoji: '🤑', name: 'money-mouth face', shortcodes: ['money_mouth_face'] },
  { emoji: '🤗', name: 'hugging face', shortcodes: ['hugs'] },
  { emoji: '🤭', name: 'face with hand over mouth', shortcodes: ['hand_over_mouth'] },
  { emoji: '🤫', name: 'shushing face', shortcodes: ['shushing_face'] },
  { emoji: '🤔', name: 'thinking face', shortcodes: ['thinking'] },
  { emoji: '🤐', name: 'zipper-mouth face', shortcodes: ['zipper_mouth_face'] },
  { emoji: '🤨', name: 'face with raised eyebrow', shortcodes: ['raised_eyebrow'] },
  { emoji: '😐', name: 'neutral face', shortcodes: ['neutral_face'] },
  { emoji: '😑', name: 'expressionless face', shortcodes: ['expressionless'] },
  { emoji: '😶', name: 'face without mouth', shortcodes: ['no_mouth'] },
  { emoji: '😏', name: 'smirking face', shortcodes: ['smirk'] },
  { emoji: '😒', name: 'unamused face', shortcodes: ['unamused'] },
  { emoji: '🙄', name: 'face with rolling eyes', shortcodes: ['roll_eyes'] },
  { emoji: '😬', name: 'grimacing face', shortcodes: ['grimacing'] },
  { emoji: '🤥', name: 'lying face', shortcodes: ['lying_face'] },
  { emoji: '😔', name: 'pensive face', shortcodes: ['pensive'] },
  { emoji: '😪', name: 'sleepy face', shortcodes: ['sleepy'] },
  { emoji: '🤤', name: 'drooling face', shortcodes: ['drooling_face'] },
  { emoji: '😴', name: 'sleeping face', shortcodes: ['sleeping'] },
  { emoji: '😷', name: 'face with medical mask', shortcodes: ['mask'] },
  { emoji: '🤒', name: 'face with thermometer', shortcodes: ['face_with_thermometer'] },
  { emoji: '🤕', name: 'face with head-bandage', shortcodes: ['face_with_head_bandage'] },
  { emoji: '🤢', name: 'nauseated face', shortcodes: ['nauseated_face'] },
  { emoji: '🤮', name: 'face vomiting', shortcodes: ['vomiting_face'] },
  { emoji: '🤧', name: 'sneezing face', shortcodes: ['sneezing_face'] },
  { emoji: '🥵', name: 'hot face', shortcodes: ['hot_face'] },
  { emoji: '🥶', name: 'cold face', shortcodes: ['cold_face'] },
  { emoji: '🥴', name: 'woozy face', shortcodes: ['woozy_face'] },
  { emoji: '😵', name: 'dizzy face', shortcodes: ['dizzy_face'] },
  { emoji: '🤯', name: 'exploding head', shortcodes: ['exploding_head'] },
  { emoji: '🤠', name: 'cowboy hat face', shortcodes: ['cowboy_hat_face'] },
  { emoji: '🥳', name: 'partying face', shortcodes: ['partying_face'] },
  { emoji: '🥸', name: 'disguised face', shortcodes: ['disguised_face'] },
  { emoji: '😎', name: 'smiling face with sunglasses', shortcodes: ['sunglasses'] },
  { emoji: '🤓', name: 'nerd face', shortcodes: ['nerd_face'] },
  { emoji: '🧐', name: 'face with monocle', shortcodes: ['monocle_face'] },
  { emoji: '😕', name: 'confused face', shortcodes: ['confused'] },
  { emoji: '😟', name: 'worried face', shortcodes: ['worried'] },
  { emoji: '🙁', name: 'slightly frowning face', shortcodes: ['slightly_frowning_face'] },
  { emoji: '☹️', name: 'frowning face', shortcodes: ['frowning_face'] },
  { emoji: '😮', name: 'face with open mouth', shortcodes: ['open_mouth'] },
  { emoji: '😯', name: 'hushed face', shortcodes: ['hushed'] },
  { emoji: '😲', name: 'astonished face', shortcodes: ['astonished'] },
  { emoji: '😳', name: 'flushed face', shortcodes: ['flushed'] },
  { emoji: '🥺', name: 'pleading face', shortcodes: ['pleading_face'] },
  { emoji: '😦', name: 'frowning face with open mouth', shortcodes: ['frowning'] },
  { emoji: '😧', name: 'anguished face', shortcodes: ['anguished'] },
  { emoji: '😨', name: 'fearful face', shortcodes: ['fearful'] },
  { emoji: '😰', name: 'anxious face with sweat', shortcodes: ['cold_sweat'] },
  { emoji: '😥', name: 'sad but relieved face', shortcodes: ['disappointed_relieved'] },
  { emoji: '😢', name: 'crying face', shortcodes: ['cry'] },
  { emoji: '😭', name: 'loudly crying face', shortcodes: ['sob'] },
  { emoji: '😱', name: 'face screaming in fear', shortcodes: ['scream'] },
  { emoji: '😖', name: 'confounded face', shortcodes: ['confounded'] },
  { emoji: '😣', name: 'persevering face', shortcodes: ['persevere'] },
  { emoji: '😞', name: 'disappointed face', shortcodes: ['disappointed'] },
  { emoji: '😓', name: 'downcast face with sweat', shortcodes: ['sweat'] },
  { emoji: '😩', name: 'weary face', shortcodes: ['weary'] },
  { emoji: '😫', name: 'tired face', shortcodes: ['tired_face'] },
  { emoji: '🥱', name: 'yawning face', shortcodes: ['yawning_face'] },
  { emoji: '😤', name: 'face with steam from nose', shortcodes: ['triumph'] },
  { emoji: '😡', name: 'pouting face', shortcodes: ['rage'] },
  { emoji: '😠', name: 'angry face', shortcodes: ['angry'] },
  { emoji: '🤬', name: 'face with symbols on mouth', shortcodes: ['cursing_face'] },
  { emoji: '😈', name: 'smiling face with horns', shortcodes: ['smiling_imp'] },
  { emoji: '👿', name: 'angry face with horns', shortcodes: ['imp'] },
  { emoji: '💀', name: 'skull', shortcodes: ['skull'] },
  { emoji: '☠️', name: 'skull and crossbones', shortcodes: ['skull_and_crossbones'] },
  // Hand gestures
  { emoji: '👍', name: 'thumbs up', shortcodes: ['thumbsup', '+1'] },
  { emoji: '👎', name: 'thumbs down', shortcodes: ['thumbsdown', '-1'] },
  { emoji: '👌', name: 'OK hand', shortcodes: ['ok_hand'] },
  { emoji: '✌️', name: 'victory hand', shortcodes: ['v'] },
  { emoji: '🤞', name: 'crossed fingers', shortcodes: ['crossed_fingers'] },
  { emoji: '🤟', name: 'love-you gesture', shortcodes: ['love_you_gesture'] },
  { emoji: '🤘', name: 'sign of the horns', shortcodes: ['metal'] },
  { emoji: '🤙', name: 'call me hand', shortcodes: ['call_me_hand'] },
  { emoji: '👈', name: 'backhand index pointing left', shortcodes: ['point_left'] },
  { emoji: '👉', name: 'backhand index pointing right', shortcodes: ['point_right'] },
  { emoji: '👆', name: 'backhand index pointing up', shortcodes: ['point_up_2'] },
  { emoji: '🖕', name: 'middle finger', shortcodes: ['middle_finger'] },
  { emoji: '👇', name: 'backhand index pointing down', shortcodes: ['point_down'] },
  { emoji: '☝️', name: 'index pointing up', shortcodes: ['point_up'] },
  { emoji: '👋', name: 'waving hand', shortcodes: ['wave'] },
  { emoji: '🤚', name: 'raised back of hand', shortcodes: ['raised_back_of_hand'] },
  { emoji: '🖐️', name: 'hand with fingers splayed', shortcodes: ['raised_hand_with_fingers_splayed'] },
  { emoji: '✋', name: 'raised hand', shortcodes: ['hand', 'raised_hand'] },
  { emoji: '🖖', name: 'vulcan salute', shortcodes: ['vulcan_salute'] },
  { emoji: '👏', name: 'clapping hands', shortcodes: ['clap'] },
  { emoji: '🙌', name: 'raising hands', shortcodes: ['raised_hands'] },
  { emoji: '👐', name: 'open hands', shortcodes: ['open_hands'] },
  { emoji: '🤲', name: 'palms up together', shortcodes: ['palms_up_together'] },
  { emoji: '🤝', name: 'handshake', shortcodes: ['handshake'] },
  { emoji: '🙏', name: 'folded hands', shortcodes: ['pray'] },
  // Hearts and symbols
  { emoji: '❤️', name: 'red heart', shortcodes: ['heart'] },
  { emoji: '🧡', name: 'orange heart', shortcodes: ['orange_heart'] },
  { emoji: '💛', name: 'yellow heart', shortcodes: ['yellow_heart'] },
  { emoji: '💚', name: 'green heart', shortcodes: ['green_heart'] },
  { emoji: '💙', name: 'blue heart', shortcodes: ['blue_heart'] },
  { emoji: '💜', name: 'purple heart', shortcodes: ['purple_heart'] },
  { emoji: '🖤', name: 'black heart', shortcodes: ['black_heart'] },
  { emoji: '🤍', name: 'white heart', shortcodes: ['white_heart'] },
  { emoji: '🤎', name: 'brown heart', shortcodes: ['brown_heart'] },
  { emoji: '💔', name: 'broken heart', shortcodes: ['broken_heart'] },
  { emoji: '❣️', name: 'heart exclamation', shortcodes: ['heavy_heart_exclamation'] },
  { emoji: '💕', name: 'two hearts', shortcodes: ['two_hearts'] },
  { emoji: '💞', name: 'revolving hearts', shortcodes: ['revolving_hearts'] },
  { emoji: '💓', name: 'beating heart', shortcodes: ['heartbeat'] },
  { emoji: '💗', name: 'growing heart', shortcodes: ['heartpulse'] },
  { emoji: '💖', name: 'sparkling heart', shortcodes: ['sparkling_heart'] },
  { emoji: '💘', name: 'heart with arrow', shortcodes: ['cupid'] },
  { emoji: '💝', name: 'heart with ribbon', shortcodes: ['gift_heart'] },
  { emoji: '💟', name: 'heart decoration', shortcodes: ['heart_decoration'] },
  // Common objects and symbols
  { emoji: '🎉', name: 'party popper', shortcodes: ['tada'] },
  { emoji: '🎊', name: 'confetti ball', shortcodes: ['confetti_ball'] },
  { emoji: '🔥', name: 'fire', shortcodes: ['fire'] },
  { emoji: '💯', name: 'hundred points', shortcodes: ['100'] },
  { emoji: '🚀', name: 'rocket', shortcodes: ['rocket'] },
  { emoji: '⭐', name: 'star', shortcodes: ['star'] },
  { emoji: '🌟', name: 'glowing star', shortcodes: ['star2'] },
  { emoji: '💡', name: 'light bulb', shortcodes: ['bulb'] },
  { emoji: '⚡', name: 'high voltage', shortcodes: ['zap'] },
  { emoji: '💥', name: 'collision', shortcodes: ['boom', 'collision'] },
  { emoji: '💫', name: 'dizzy', shortcodes: ['dizzy'] },
  { emoji: '💦', name: 'sweat droplets', shortcodes: ['sweat_drops'] },
  { emoji: '💨', name: 'dashing away', shortcodes: ['dash'] },
  { emoji: '🎯', name: 'direct hit', shortcodes: ['dart'] },
  { emoji: '🎪', name: 'circus tent', shortcodes: ['circus_tent'] },
  { emoji: '🎭', name: 'performing arts', shortcodes: ['performing_arts'] },
  { emoji: '🎨', name: 'artist palette', shortcodes: ['art'] },
  { emoji: '🎬', name: 'clapper board', shortcodes: ['clapper'] },
  { emoji: '🎤', name: 'microphone', shortcodes: ['microphone'] },
  { emoji: '🎧', name: 'headphone', shortcodes: ['headphones'] },
  { emoji: '🎵', name: 'musical note', shortcodes: ['musical_note'] },
  { emoji: '🎶', name: 'musical notes', shortcodes: ['notes'] },
  { emoji: '🎸', name: 'guitar', shortcodes: ['guitar'] },
  { emoji: '🎹', name: 'musical keyboard', shortcodes: ['musical_keyboard'] },
  { emoji: '🎺', name: 'trumpet', shortcodes: ['trumpet'] },
  { emoji: '🎻', name: 'violin', shortcodes: ['violin'] },
  { emoji: '🥁', name: 'drum', shortcodes: ['drum'] },
]

export function useEmojiAutocomplete() {
  const searchQuery = ref('')
  const isVisible = ref(false)
  const selectedIndex = ref(0)

  const filteredEmojis = computed(() => {
    if (!searchQuery.value) return []
    
    const query = searchQuery.value.toLowerCase()
    return emojiDatabase.filter(emoji => 
      emoji.shortcodes.some(shortcode => shortcode.includes(query)) ||
      emoji.name.toLowerCase().includes(query)
    ).slice(0, 10) // Limit to 10 results
  })

  const showAutocomplete = (query: string) => {
    searchQuery.value = query
    isVisible.value = true
    selectedIndex.value = 0
  }

  const hideAutocomplete = () => {
    isVisible.value = false
    searchQuery.value = ''
    selectedIndex.value = 0
  }

  const selectNext = () => {
    if (filteredEmojis.value.length > 0) {
      selectedIndex.value = (selectedIndex.value + 1) % filteredEmojis.value.length
    }
  }

  const selectPrevious = () => {
    if (filteredEmojis.value.length > 0) {
      selectedIndex.value = selectedIndex.value === 0 
        ? filteredEmojis.value.length - 1 
        : selectedIndex.value - 1
    }
  }

  const getSelectedEmoji = () => {
    return filteredEmojis.value[selectedIndex.value] || null
  }

  return {
    searchQuery,
    isVisible,
    selectedIndex,
    filteredEmojis,
    showAutocomplete,
    hideAutocomplete,
    selectNext,
    selectPrevious,
    getSelectedEmoji
  }
}
