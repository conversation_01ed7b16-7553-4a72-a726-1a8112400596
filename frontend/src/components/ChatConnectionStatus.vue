<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import echo from '@/services/echo'

const isConnected = ref(false)

// Update connection status
const updateConnectionStatus = () => {
  try {
    isConnected.value = echo.connector.pusher.connection.state === 'connected'
  } catch (error) {
    console.error('Error checking connection status:', error)
    isConnected.value = false
  }
}

onMounted(() => {
  // Initial check
  updateConnectionStatus()

  // Set up event listeners
  if (echo.connector.pusher) {
    echo.connector.pusher.connection.bind('connected', () => {
      isConnected.value = true
    })

    echo.connector.pusher.connection.bind('disconnected', () => {
      isConnected.value = false
    })

    echo.connector.pusher.connection.bind('error', () => {
      isConnected.value = false
    })
  }

  // Periodic check (every 5 seconds)
  const interval = setInterval(updateConnectionStatus, 5000)

  onUnmounted(() => {
    clearInterval(interval)
    if (echo.connector.pusher) {
      echo.connector.pusher.connection.unbind('connected')
      echo.connector.pusher.connection.unbind('disconnected')
      echo.connector.pusher.connection.unbind('error')
    }
  })
})
</script>

<template>
  <div
    class="connection-status-dot"
    :class="{
      'online': isConnected,
      'offline': !isConnected
    }"
  ></div>
</template>

<style scoped>
.connection-status-dot {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  transition: background-color 0.3s ease;
}

.connection-status-dot.online {
  background-color: #4CAF50;
}

.connection-status-dot.offline {
  background-color: #F44336;
}
</style>
