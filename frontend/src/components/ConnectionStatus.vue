<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import echo from '@/services/echo'

const isConnected = ref(false)
const isAuthenticated = ref(false)
const socketId = ref('')

// Update connection status
const updateConnectionStatus = () => {
  try {
    isConnected.value = echo.connector.pusher.connection.state === 'connected'
    socketId.value = echo.socketId() || ''
    isAuthenticated.value = !!socketId.value
  } catch (error) {
    console.error('Error checking connection status:', error)
    isConnected.value = false
    isAuthenticated.value = false
  }
}

onMounted(() => {
  // Initial check
  updateConnectionStatus()

  // Set up event listeners
  if (echo.connector.pusher) {
    echo.connector.pusher.connection.bind('connected', () => {
      isConnected.value = true
      socketId.value = echo.socketId() || ''
      isAuthenticated.value = !!socketId.value
    })

    echo.connector.pusher.connection.bind('disconnected', () => {
      isConnected.value = false
      isAuthenticated.value = false
    })

    echo.connector.pusher.connection.bind('error', () => {
      isConnected.value = false
      isAuthenticated.value = false
    })
  }

  // Periodic check (every 5 seconds)
  const interval = setInterval(updateConnectionStatus, 5000)

  onUnmounted(() => {
    clearInterval(interval)
    if (echo.connector.pusher) {
      echo.connector.pusher.connection.unbind('connected')
      echo.connector.pusher.connection.unbind('disconnected')
      echo.connector.pusher.connection.unbind('error')
    }
  })
})
</script>

<template>
  <div class="connection-status">
    <div class="status-indicator" :class="{
      'connected': isConnected,
      'disconnected': !isConnected,
      'authenticated': isAuthenticated
    }"></div>
    <span class="status-text">{{ isConnected ? 'Connected' : 'Disconnected' }}</span>
    <span v-if="isConnected && isAuthenticated" class="socket-id">ID: {{ socketId.substring(0, 6) }}...</span>
  </div>
</template>

<style scoped>
.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.connected {
  background-color: #4CAF50;
}

.disconnected {
  background-color: #F44336;
}

.authenticated {
  box-shadow: 0 0 5px #4CAF50;
}

.status-text {
  font-weight: 500;
}

.socket-id {
  color: #666;
  font-size: 0.7rem;
}
</style>
