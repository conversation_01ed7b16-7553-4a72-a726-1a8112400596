<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'

interface Props {
  imageUrl: string
  cropSize?: number
}

interface Emits {
  (e: 'crop', croppedFile: File): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  cropSize: 200
})

const emit = defineEmits<Emits>()

const canvas = ref<HTMLCanvasElement>()
const cropCanvas = ref<HTMLCanvasElement>()
const image = ref<HTMLImageElement>()
const isDragging = ref(false)
const isResizing = ref(false)

// Fixed crop area (centered circle)
const cropArea = ref({
  x: 0,
  y: 0,
  size: 250
})

// Image state
const imageState = ref({
  x: 0,
  y: 0,
  scale: 1,
  naturalWidth: 0,
  naturalHeight: 0
})

const dragStart = ref({ x: 0, y: 0 })
const isDraggingImage = ref(false)

onMounted(async () => {
  await nextTick()
  loadImage()
})

const loadImage = () => {
  const img = new Image()
  img.onload = () => {
    imageState.value.naturalWidth = img.naturalWidth
    imageState.value.naturalHeight = img.naturalHeight

    // Calculate initial scale to fit image in canvas
    const canvasWidth = canvas.value?.width || 450
    const canvasHeight = canvas.value?.height || 450
    const scaleX = canvasWidth / img.naturalWidth
    const scaleY = canvasHeight / img.naturalHeight
    imageState.value.scale = Math.min(scaleX, scaleY, 1)

    // Center the image
    imageState.value.x = (canvasWidth - img.naturalWidth * imageState.value.scale) / 2
    imageState.value.y = (canvasHeight - img.naturalHeight * imageState.value.scale) / 2

    // Center the crop area (fixed position)
    cropArea.value.x = (canvasWidth - cropArea.value.size) / 2
    cropArea.value.y = (canvasHeight - cropArea.value.size) / 2

    image.value = img
    draw()
  }
  img.src = props.imageUrl
}

const draw = () => {
  if (!canvas.value || !image.value) return

  const ctx = canvas.value.getContext('2d')
  if (!ctx) return

  // Clear canvas
  ctx.clearRect(0, 0, canvas.value.width, canvas.value.height)

  // Draw image
  ctx.drawImage(
    image.value,
    imageState.value.x,
    imageState.value.y,
    image.value.naturalWidth * imageState.value.scale,
    image.value.naturalHeight * imageState.value.scale
  )

  // Draw overlay
  ctx.fillStyle = 'rgba(0, 0, 0, 0.5)'
  ctx.fillRect(0, 0, canvas.value.width, canvas.value.height)

  // Clear crop area
  ctx.globalCompositeOperation = 'destination-out'
  ctx.beginPath()
  ctx.arc(
    cropArea.value.x + cropArea.value.size / 2,
    cropArea.value.y + cropArea.value.size / 2,
    cropArea.value.size / 2,
    0,
    2 * Math.PI
  )
  ctx.fill()

  // Draw crop circle border
  ctx.globalCompositeOperation = 'source-over'
  ctx.strokeStyle = '#fff'
  ctx.lineWidth = 2
  ctx.beginPath()
  ctx.arc(
    cropArea.value.x + cropArea.value.size / 2,
    cropArea.value.y + cropArea.value.size / 2,
    cropArea.value.size / 2,
    0,
    2 * Math.PI
  )
  ctx.stroke()
}

const handleMouseDown = (event: MouseEvent) => {
  const rect = canvas.value?.getBoundingClientRect()
  if (!rect) return

  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  // Start dragging the image
  isDraggingImage.value = true
  dragStart.value = { x: x - imageState.value.x, y: y - imageState.value.y }
}

const handleMouseMove = (event: MouseEvent) => {
  if (!isDraggingImage.value || !canvas.value) return

  const rect = canvas.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  // Move the image
  imageState.value.x = x - dragStart.value.x
  imageState.value.y = y - dragStart.value.y

  draw()
}

const handleMouseUp = () => {
  isDraggingImage.value = false
}

const handleZoomChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const newScale = parseFloat(target.value)

  // Calculate the center of the crop area
  const centerX = cropArea.value.x + cropArea.value.size / 2
  const centerY = cropArea.value.y + cropArea.value.size / 2

  // Calculate the current center of the image
  const currentImageCenterX = imageState.value.x + (imageState.value.naturalWidth * imageState.value.scale) / 2
  const currentImageCenterY = imageState.value.y + (imageState.value.naturalHeight * imageState.value.scale) / 2

  // Update scale
  imageState.value.scale = newScale

  // Adjust image position to keep it centered on the crop area
  imageState.value.x = centerX - (imageState.value.naturalWidth * newScale) / 2
  imageState.value.y = centerY - (imageState.value.naturalHeight * newScale) / 2

  draw()
}

const cropImage = () => {
  if (!canvas.value || !image.value || !cropCanvas.value) return

  const ctx = cropCanvas.value.getContext('2d')
  if (!ctx) return

  // Set crop canvas size
  cropCanvas.value.width = props.cropSize
  cropCanvas.value.height = props.cropSize

  // Calculate source coordinates
  const scaleRatio = 1 / imageState.value.scale
  const sourceX = (cropArea.value.x - imageState.value.x) * scaleRatio
  const sourceY = (cropArea.value.y - imageState.value.y) * scaleRatio
  const sourceSize = cropArea.value.size * scaleRatio

  // Draw cropped image
  ctx.drawImage(
    image.value,
    sourceX,
    sourceY,
    sourceSize,
    sourceSize,
    0,
    0,
    props.cropSize,
    props.cropSize
  )

  // Convert to blob and emit
  cropCanvas.value.toBlob((blob) => {
    if (blob) {
      const file = new File([blob], 'cropped-image.jpg', { type: 'image/jpeg' })
      emit('crop', file)
    }
  }, 'image/jpeg', 0.9)
}

const cancel = () => {
  emit('cancel')
}
</script>

<template>
  <div class="image-cropper">
    <div class="cropper-container">
      <canvas
        ref="canvas"
        width="450"
        height="450"
        @mousedown="handleMouseDown"
        @mousemove="handleMouseMove"
        @mouseup="handleMouseUp"
        @mouseleave="handleMouseUp"
      ></canvas>
      <canvas ref="cropCanvas" style="display: none;"></canvas>
    </div>

    <div class="controls">
      <div class="zoom-controls">
        <label for="zoom-slider">Zoom:</label>
        <input
          id="zoom-slider"
          type="range"
          min="0.1"
          max="3"
          step="0.1"
          :value="imageState.scale"
          @input="handleZoomChange"
          class="zoom-slider"
        />
        <span class="zoom-value">{{ Math.round(imageState.scale * 100) }}%</span>
      </div>

      <div class="action-buttons">
        <button @click="cropImage" class="btn btn-primary">Crop & Upload</button>
        <button @click="cancel" class="btn btn-secondary">Cancel</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.image-cropper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.cropper-container {
  position: relative;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

canvas {
  display: block;
  cursor: move;
}

.controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
  max-width: 300px;
}

.zoom-controls label {
  font-weight: 500;
  color: #555;
  white-space: nowrap;
}

.zoom-slider {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #ddd;
  outline: none;
  cursor: pointer;
}

.zoom-slider::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #42b883;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.zoom-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #42b883;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.zoom-value {
  font-size: 0.9rem;
  color: #666;
  min-width: 40px;
  text-align: right;
}

.action-buttons {
  display: flex;
  gap: 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
}

.btn-primary {
  background-color: #42b883;
  color: white;
}

.btn-primary:hover {
  background-color: #369870;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
}
</style>
