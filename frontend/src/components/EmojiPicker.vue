<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// Define emits for parent communication
const emit = defineEmits(['emoji-selected', 'close'])

interface EmojiData {
  emoji: string
  name: string
  shortcodes: string[]
}

const emojiPickerRef = ref<HTMLElement>()
const isVisible = ref(false)

// Common emojis for quick access
const commonEmojis = [
  { emoji: '😀', name: 'grinning face', shortcodes: ['grinning'] },
  { emoji: '😂', name: 'face with tears of joy', shortcodes: ['joy'] },
  { emoji: '😍', name: 'smiling face with heart-eyes', shortcodes: ['heart_eyes'] },
  { emoji: '🤔', name: 'thinking face', shortcodes: ['thinking'] },
  { emoji: '😎', name: 'smiling face with sunglasses', shortcodes: ['sunglasses'] },
  { emoji: '😢', name: 'crying face', shortcodes: ['cry'] },
  { emoji: '😡', name: 'pouting face', shortcodes: ['rage'] },
  { emoji: '👍', name: 'thumbs up', shortcodes: ['thumbsup', '+1'] },
  { emoji: '👎', name: 'thumbs down', shortcodes: ['thumbsdown', '-1'] },
  { emoji: '❤️', name: 'red heart', shortcodes: ['heart'] },
  { emoji: '🎉', name: 'party popper', shortcodes: ['tada'] },
  { emoji: '🔥', name: 'fire', shortcodes: ['fire'] },
  { emoji: '💯', name: 'hundred points', shortcodes: ['100'] },
  { emoji: '🚀', name: 'rocket', shortcodes: ['rocket'] },
  { emoji: '⭐', name: 'star', shortcodes: ['star'] },
  { emoji: '💡', name: 'light bulb', shortcodes: ['bulb'] },
]

const selectEmoji = (emojiData: EmojiData) => {
  emit('emoji-selected', emojiData.emoji)
  close()
}

const close = () => {
  isVisible.value = false
  emit('close')
}

const show = () => {
  isVisible.value = true
}

const handleClickOutside = (event: MouseEvent) => {
  if (emojiPickerRef.value && !emojiPickerRef.value.contains(event.target as Node)) {
    close()
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// Expose methods to parent
defineExpose({
  show,
  close
})
</script>

<template>
  <div v-if="isVisible" ref="emojiPickerRef" class="emoji-picker">
    <div class="emoji-picker-header">
      <span class="emoji-picker-title">Pick an emoji</span>
      <button @click="close" class="emoji-picker-close">×</button>
    </div>
    <div class="emoji-picker-content">
      <div class="emoji-grid">
        <button
          v-for="emojiData in commonEmojis"
          :key="emojiData.emoji"
          @click="selectEmoji(emojiData)"
          class="emoji-button"
          :title="emojiData.name"
        >
          {{ emojiData.emoji }}
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.emoji-picker {
  position: absolute;
  bottom: 100%;
  right: 0;
  width: 280px;
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-bottom: 8px;
}

.emoji-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e1e5e9;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.emoji-picker-title {
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.emoji-picker-close {
  background: none;
  border: none;
  font-size: 20px;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.emoji-picker-close:hover {
  background: #e9ecef;
  color: #495057;
}

.emoji-picker-content {
  padding: 16px;
  max-height: 200px;
  overflow-y: auto;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 4px;
}

.emoji-button {
  background: none;
  border: none;
  font-size: 20px;
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 36px;
}

.emoji-button:hover {
  background: #f8f9fa;
}

.emoji-button:active {
  background: #e9ecef;
}
</style>
