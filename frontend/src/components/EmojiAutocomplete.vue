<script setup lang="ts">
import { computed } from 'vue'
import type { EmojiData } from '@/composables/useEmojiAutocomplete'

interface Props {
  emojis: EmojiData[]
  selectedIndex: number
  isVisible: boolean
}

const props = defineProps<Props>()
const emit = defineEmits(['emoji-selected', 'close'])

const selectEmoji = (emoji: EmojiData, index: number) => {
  emit('emoji-selected', emoji, index)
}

const visibleEmojis = computed(() => props.emojis.slice(0, 10))
</script>

<template>
  <div v-if="isVisible && visibleEmojis.length > 0" class="emoji-autocomplete">
    <div
      v-for="(emoji, index) in visibleEmojis"
      :key="emoji.emoji"
      @click="selectEmoji(emoji, index)"
      class="emoji-autocomplete-item"
      :class="{ 'selected': index === selectedIndex }"
    >
      <span class="emoji-autocomplete-emoji">{{ emoji.emoji }}</span>
      <span class="emoji-autocomplete-name">:{{ emoji.shortcodes[0] }}:</span>
      <span class="emoji-autocomplete-description">{{ emoji.name }}</span>
    </div>
  </div>
</template>

<style scoped>
.emoji-autocomplete {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-bottom: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.emoji-autocomplete-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f1f3f4;
}

.emoji-autocomplete-item:last-child {
  border-bottom: none;
}

.emoji-autocomplete-item:hover,
.emoji-autocomplete-item.selected {
  background: #f8f9fa;
}

.emoji-autocomplete-emoji {
  font-size: 18px;
  margin-right: 8px;
  flex-shrink: 0;
}

.emoji-autocomplete-name {
  font-family: 'Courier New', monospace;
  color: #007bff;
  font-weight: 600;
  margin-right: 8px;
  flex-shrink: 0;
}

.emoji-autocomplete-description {
  color: #6c757d;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
