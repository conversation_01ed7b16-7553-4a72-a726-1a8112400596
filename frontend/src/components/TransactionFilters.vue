<script setup lang="ts">
import { ref, watch } from 'vue'

interface FilterOptions {
  dateRange: string
  typeFilter: string
  gameFilter: string
  currencyFilter: string
}

const props = defineProps<{
  modelValue: FilterOptions
}>()

const emit = defineEmits<{
  'update:modelValue': [value: FilterOptions]
}>()

// Local reactive copies
const dateRange = ref(props.modelValue.dateRange)
const typeFilter = ref(props.modelValue.typeFilter)
const gameFilter = ref(props.modelValue.gameFilter)
const currencyFilter = ref(props.modelValue.currencyFilter)

// Watch for changes and emit updates
watch([dateRange, typeFilter, gameFilter, currencyFilter], () => {
  emit('update:modelValue', {
    dateRange: dateRange.value,
    typeFilter: typeFilter.value,
    gameFilter: gameFilter.value,
    currencyFilter: currencyFilter.value,
  })
})

// Date range options
const dateRangeOptions = [
  { value: 'all', label: 'All Time' },
  { value: '1h', label: 'Last Hour' },
  { value: '1d', label: 'Last Day' },
  { value: '30d', label: 'Last 30 Days' },
]

// Transaction type options
const typeFilterOptions = [
  { value: 'all', label: 'All Types' },
  { value: 'deposits', label: 'Deposits' },
  { value: 'withdrawals', label: 'Withdrawals' },
  { value: 'bets', label: 'Bets' },
  { value: 'wins', label: 'Wins' },
]

// Game filter options
const gameFilterOptions = [
  { value: '', label: 'All Games' },
  { value: 'dice', label: 'Dice' },
  { value: 'coinflip', label: 'Coinflip' },
  // Add more games as they are implemented
]

// Currency filter options
const currencyFilterOptions = [
  { value: '', label: 'All Currencies' },
  { value: 'USD', label: 'USD' },
  { value: 'USDT', label: 'USDT' },
  { value: 'POINTS', label: 'POINTS' },
]

const resetFilters = () => {
  dateRange.value = 'all'
  typeFilter.value = 'all'
  gameFilter.value = ''
  currencyFilter.value = ''
}


</script>

<template>
  <div class="transaction-filters">
    <div class="filters-row">
      <!-- Date Range Filter -->
      <div class="filter-group">
        <label for="date-range">Time Period</label>
        <select id="date-range" v-model="dateRange" class="filter-select">
          <option v-for="option in dateRangeOptions" :key="option.value" :value="option.value">
            {{ option.label }}
          </option>
        </select>
      </div>

      <!-- Transaction Type Filter -->
      <div class="filter-group">
        <label for="type-filter">Transaction Type</label>
        <select id="type-filter" v-model="typeFilter" class="filter-select">
          <option v-for="option in typeFilterOptions" :key="option.value" :value="option.value">
            {{ option.label }}
          </option>
        </select>
      </div>

      <!-- Game Filter -->
      <div class="filter-group">
        <label for="game-filter">Game</label>
        <select id="game-filter" v-model="gameFilter" class="filter-select">
          <option v-for="option in gameFilterOptions" :key="option.value" :value="option.value">
            {{ option.label }}
          </option>
        </select>
      </div>

      <!-- Currency Filter -->
      <div class="filter-group">
        <label for="currency-filter">Currency</label>
        <select id="currency-filter" v-model="currencyFilter" class="filter-select">
          <option v-for="option in currencyFilterOptions" :key="option.value" :value="option.value">
            {{ option.label }}
          </option>
        </select>
      </div>

      <!-- Reset Button -->
      <div class="filter-group">
        <button @click="resetFilters" class="btn btn-secondary reset-btn">
          Reset Filters
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.transaction-filters {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

.filters-row {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1rem;
  align-items: end;
}

@media (max-width: 1200px) {
  .filters-row {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 900px) {
  .filters-row {
    grid-template-columns: repeat(2, 1fr);
  }
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 500;
  color: #555;
  font-size: 0.9rem;
}

.filter-select {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  background: white;
  transition: border-color 0.2s;
}

.filter-select:focus {
  outline: none;
  border-color: #42b883;
}

.reset-btn {
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s;
  background-color: #6c757d;
  color: white;
  height: fit-content;
}

.reset-btn:hover {
  background-color: #5a6268;
}

/* Responsive */
@media (max-width: 768px) {
  .filters-row {
    grid-template-columns: 1fr;
  }

  .reset-btn {
    margin-top: 1rem;
  }
}
</style>
