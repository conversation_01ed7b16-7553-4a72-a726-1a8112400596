<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useBalanceStore } from '@/stores/balance'
import { useAuthStore } from '@/stores/auth'

const balanceStore = useBalanceStore()
const authStore = useAuthStore()

const isDropdownOpen = ref(false)
const dropdownRef = ref<HTMLElement>()

// Animation state
const animatedBalance = ref(0)
const isAnimating = ref(false)
const flashClass = ref('')
const previousBalance = ref(0)
const previousCurrency = ref('')

// Computed properties
const displayBalance = computed(() => {
  if (!balanceStore.currentBalance) {
    return {
      formatted: animatedBalance.value.toFixed(0), // POINTS has 0 decimals
      symbol: 'PTS',
      code: 'POINTS'
    }
  }

  const decimals = balanceStore.currentBalance.decimals

  return {
    formatted: animatedBalance.value.toFixed(decimals),
    symbol: balanceStore.currentBalance.symbol,
    code: balanceStore.currentBalance.currency_code
  }
})

const isLoading = computed(() => balanceStore.isLoading)

// Animation state
let currentAnimationId: number | null = null

// Animation functions
const animateBalance = (fromValue: number, toValue: number, shouldFlash: boolean = true, duration: number = 300) => {
  // Cancel any existing animation to allow new updates to take precedence
  if (currentAnimationId !== null) {
    cancelAnimationFrame(currentAnimationId)
    currentAnimationId = null
  }

  isAnimating.value = true
  const startTime = Date.now()
  const difference = toValue - fromValue

  // Determine flash color based on change (only if shouldFlash is true)
  if (shouldFlash && difference !== 0) {
    if (difference > 0) {
      flashClass.value = 'flash-green'
    } else if (difference < 0) {
      flashClass.value = 'flash-red'
    }
  }

  const animate = () => {
    const elapsed = Date.now() - startTime
    const progress = Math.min(elapsed / duration, 1)

    // Easing function (ease-out)
    const easeOut = 1 - Math.pow(1 - progress, 3)

    animatedBalance.value = fromValue + (difference * easeOut)

    if (progress < 1) {
      currentAnimationId = requestAnimationFrame(animate)
    } else {
      animatedBalance.value = toValue
      isAnimating.value = false
      currentAnimationId = null

      // Clear flash after animation (only if we flashed)
      if (shouldFlash) {
        setTimeout(() => {
          flashClass.value = ''
        }, 200)
      }
    }
  }

  currentAnimationId = requestAnimationFrame(animate)
}

// Methods
function toggleDropdown() {
  isDropdownOpen.value = !isDropdownOpen.value
}

function selectCurrency(currencyCode: string) {
  balanceStore.selectCurrency(currencyCode)
  isDropdownOpen.value = false
}

function closeDropdown() {
  isDropdownOpen.value = false
}

// Close dropdown when clicking outside
function handleClickOutside(event: Event) {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    closeDropdown()
  }
}

// Watch for balance changes - this will trigger for both real updates and currency switches
watch(() => ({
  balance: balanceStore.currentBalance?.formatted,
  currency: balanceStore.selectedCurrency
}), (newData, oldData) => {
  const newValue = newData.balance !== undefined ? parseFloat(newData.balance.toString()) : 0
  const oldValue = oldData?.balance !== undefined ? parseFloat(oldData.balance.toString()) : 0

  // Check if this is a currency change vs a balance update
  const isCurrencyChange = oldData?.currency && oldData.currency !== newData.currency
  const isBalanceChange = !isNaN(newValue) && !isNaN(oldValue) && newValue !== oldValue && !isCurrencyChange

  if (isBalanceChange && oldData?.currency === newData.currency) {
    // Real balance update for the same currency - animate with flash
    animateBalance(oldValue, newValue, true)
  } else if (isCurrencyChange) {
    // Currency switch - animate without flash
    animateBalance(animatedBalance.value, newValue, false)
  } else if (oldData?.balance === undefined) {
    // Initial load - set without animation
    animatedBalance.value = newValue
  }

  // Update tracking values
  previousBalance.value = newValue
  previousCurrency.value = newData.currency
})

onMounted(() => {
  document.addEventListener('click', handleClickOutside)

  // Initialize balance store if authenticated
  if (authStore.isAuthenticated) {
    balanceStore.initialize()
  }

  // Set initial animated balance and tracking values
  if (balanceStore.currentBalance) {
    animatedBalance.value = balanceStore.currentBalance.formatted
    previousBalance.value = animatedBalance.value
    previousCurrency.value = balanceStore.selectedCurrency
  }
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)

  // Cancel any ongoing animation
  if (currentAnimationId !== null) {
    cancelAnimationFrame(currentAnimationId)
    currentAnimationId = null
  }
})
</script>

<template>
  <div class="user-balance" ref="dropdownRef" v-if="authStore.isAuthenticated">
    <div class="balance-display" @click="toggleDropdown" :class="flashClass">
      <div class="balance-amount">
        <span v-if="isLoading" class="loading">...</span>
        <span v-else class="amount">{{ displayBalance.formatted }}</span>
        <span class="currency">{{ displayBalance.code }}</span>
      </div>
      <div class="dropdown-arrow" :class="{ 'open': isDropdownOpen }">▼</div>
    </div>

    <div class="currency-dropdown" v-show="isDropdownOpen">
      <div
        v-for="currency in balanceStore.availableCurrencies"
        :key="currency.code"
        class="currency-option"
        :class="{ 'selected': currency.code === balanceStore.selectedCurrency }"
        @click="selectCurrency(currency.code)"
      >
        <span class="currency-amount">{{ currency.formatted.toFixed(currency.decimals) }}</span>
        <span class="currency-code">{{ currency.code }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.user-balance {
  position: relative;
  display: inline-block;
}

.balance-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.balance-display:hover {
  background: #e9ecef;
  border-color: #dee2e6;
}

/* Flash animations for balance changes */
.balance-display.flash-green {
  animation: flashGreen 0.6s ease-out;
}

.balance-display.flash-red {
  animation: flashRed 0.6s ease-out;
}

@keyframes flashGreen {
  0% {
    background-color: #f8f9fa;
    border-color: #e9ecef;
  }
  30% {
    background-color: #d4edda;
    border-color: #28a745;
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.3);
  }
  100% {
    background-color: #f8f9fa;
    border-color: #e9ecef;
  }
}

@keyframes flashRed {
  0% {
    background-color: #f8f9fa;
    border-color: #e9ecef;
  }
  30% {
    background-color: #f8d7da;
    border-color: #dc3545;
    box-shadow: 0 0 10px rgba(220, 53, 69, 0.3);
  }
  100% {
    background-color: #f8f9fa;
    border-color: #e9ecef;
  }
}

.balance-amount {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-weight: 500;
}

.amount {
  color: #2c3e50;
  font-size: 0.9rem;
  font-variant-numeric: tabular-nums; /* Ensures consistent number width */
  transition: color 0.3s ease;
}

.currency {
  color: #6c757d;
  font-size: 0.8rem;
  text-transform: uppercase;
}

.loading {
  color: #6c757d;
  font-size: 0.9rem;
}

.dropdown-arrow {
  color: #6c757d;
  font-size: 0.7rem;
  transition: transform 0.2s ease;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

.currency-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 2px;
  overflow: hidden;
}

.currency-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f8f9fa;
}

.currency-option:last-child {
  border-bottom: none;
}

.currency-option:hover {
  background: #f8f9fa;
}

.currency-option.selected {
  background: #e3f2fd;
  color: #1976d2;
}

.currency-amount {
  font-weight: 500;
  font-size: 0.9rem;
}

.currency-code {
  font-size: 0.8rem;
  text-transform: uppercase;
  color: #6c757d;
}

.currency-option.selected .currency-code {
  color: #1976d2;
}

/* Responsive design */
@media (max-width: 768px) {
  .balance-display {
    min-width: 100px;
    padding: 0.4rem 0.8rem;
  }

  .amount {
    font-size: 0.8rem;
  }

  .currency {
    font-size: 0.7rem;
  }
}
</style>
