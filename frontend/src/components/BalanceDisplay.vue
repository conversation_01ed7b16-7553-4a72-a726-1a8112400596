<script setup lang="ts">
import { ref, onMounted } from 'vue'
import axios from 'axios'

interface Balance {
  currency: {
    id: number
    code: string
    symbol: string
    decimals: number
  }
  balance: number
  balance_formatted: number
  updated_at: string
}

const balances = ref<Balance[]>([])
const isLoading = ref(false)
const error = ref('')

const fetchBalances = async () => {
  isLoading.value = true
  error.value = ''

  try {
    const response = await axios.get(`${import.meta.env.VITE_API_URL}/api/balances`)
    
    if (response.data.success) {
      balances.value = response.data.data
    } else {
      error.value = 'Failed to load balances'
    }
  } catch (err: any) {
    console.error('Failed to fetch balances:', err)
    error.value = 'Failed to load balances'
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  fetchBalances()
})

const formatCurrency = (amount: number, symbol: string) => {
  return `${symbol}${amount.toFixed(2)}`
}
</script>

<template>
  <div class="balance-display">
    <h3>Current Balances</h3>
    
    <div v-if="isLoading" class="loading">
      Loading balances...
    </div>
    
    <div v-else-if="error" class="error">
      {{ error }}
      <button @click="fetchBalances" class="btn btn-primary retry-btn">
        Retry
      </button>
    </div>
    
    <div v-else class="balances-grid">
      <div 
        v-for="balance in balances" 
        :key="balance.currency.id"
        class="balance-card"
      >
        <div class="currency-info">
          <span class="currency-code">{{ balance.currency.code }}</span>
          <span class="currency-symbol">{{ balance.currency.symbol }}</span>
        </div>
        <div class="balance-amount">
          {{ formatCurrency(balance.balance_formatted, balance.currency.symbol) }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.balance-display {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

.balance-display h3 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.2rem;
}

.loading, .error {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.error {
  color: #dc3545;
}

.retry-btn {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #42b883;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.retry-btn:hover {
  background-color: #369870;
}

.balances-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.balance-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 1rem;
  text-align: center;
  transition: transform 0.2s, box-shadow 0.2s;
}

.balance-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.currency-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.currency-code {
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
}

.currency-symbol {
  background: #42b883;
  color: white;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-size: 0.8rem;
  font-weight: 500;
}

.balance-amount {
  font-size: 1.5rem;
  font-weight: 700;
  color: #28a745;
}

/* Responsive */
@media (max-width: 768px) {
  .balances-grid {
    grid-template-columns: 1fr;
  }
  
  .balance-card {
    padding: 0.75rem;
  }
  
  .balance-amount {
    font-size: 1.3rem;
  }
}
</style>
