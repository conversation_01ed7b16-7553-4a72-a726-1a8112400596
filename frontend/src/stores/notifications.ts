import { defineStore } from 'pinia'
import { ref, watch } from 'vue'
import { useAuthStore } from './auth'
import { useToastNotifications } from '@/composables/useToastNotifications'

export const useNotificationsStore = defineStore('notifications', () => {
  const authStore = useAuthStore()
  const { isListening, startListening, stopListening, reconnect } = useToastNotifications()

  const isInitialized = ref(false)
  const isInitializing = ref(false) // Track initialization in progress

  // Initialize notifications when user is authenticated
  const initialize = () => {
    if (authStore.isAuthenticated && authStore.user && !isInitialized.value && !isInitializing.value) {
      console.log('Notifications Store: Initializing toast notifications for user', authStore.user.id)
      isInitializing.value = true

      // Small delay to ensure Echo is fully connected
      setTimeout(() => {
        startListening()
        isInitialized.value = true
        isInitializing.value = false
      }, 1000)
    } else {
      console.log('Notifications Store: Cannot initialize - auth:', authStore.isAuthenticated, 'user:', !!authStore.user, 'initialized:', isInitialized.value, 'initializing:', isInitializing.value)
    }
  }

  // Cleanup notifications when user logs out
  const cleanup = () => {
    if (isInitialized.value || isInitializing.value) {
      console.log('Notifications Store: Cleaning up toast notifications')
      stopListening()
      isInitialized.value = false
      isInitializing.value = false
    }
  }

  // Single watcher for both auth state and user data
  watch(() => ({
    isAuthenticated: authStore.isAuthenticated,
    user: authStore.user
  }), ({ isAuthenticated, user }) => {
    if (isAuthenticated && user) {
      initialize()
    } else if (!isAuthenticated) {
      cleanup()
    }
  }, { immediate: true })

  return {
    isListening,
    isInitialized,
    initialize,
    cleanup,
    reconnect
  }
})
