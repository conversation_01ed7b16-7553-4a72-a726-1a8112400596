import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'

export const useAuthStore = defineStore('auth', () => {
  const user = ref<any | null>(null)
  const isInitialized = ref(false)
  const isLoading = ref(false)

  const isAuthenticated = computed(() => !!user.value)

  // Configure axios defaults for Sanctum cookie authentication
  axios.defaults.withCredentials = true;
  axios.defaults.withXSRFToken = true;

  async function login(email: string, password: string) {
    try {
      // Get CSRF cookie first (required for Sanctum)
      await axios.get(`${import.meta.env.VITE_API_URL}/sanctum/csrf-cookie`)

      const response = await axios.post(`${import.meta.env.VITE_API_URL}/api/login`, {
        username: email,
        password
      })

      user.value = response.data.user
      isInitialized.value = true

      return true
    } catch (error) {
      console.error('Login failed:', error)
      return false
    }
  }

  async function logout() {
    try {
      await axios.post(`${import.meta.env.VITE_API_URL}/api/logout`)
    } catch (error) {
      console.error('Logout API call failed:', error)
    } finally {
      // Clear auth data regardless of API success
      user.value = null
      isInitialized.value = true // Keep initialized as true after logout
    }
  }

  async function fetchUser() {
    if (isLoading.value || isInitialized.value) return user.value

    isLoading.value = true
    try {
      const response = await axios.get(`${import.meta.env.VITE_API_URL}/api/user`)

      user.value = response.data
      isInitialized.value = true
      return user.value
    } catch (error) {
      console.error('Failed to fetch user:', error)
      // If unauthorized, clear user
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        user.value = null
      }
      isInitialized.value = true
      return null
    } finally {
      isLoading.value = false
    }
  }

  async function register(username: string, password: string) {
    try {
      // Get CSRF cookie first (required for Sanctum)
      await axios.get(`${import.meta.env.VITE_API_URL}/sanctum/csrf-cookie`)

      const response = await axios.post(`${import.meta.env.VITE_API_URL}/api/register`, {
        username,
        password
      })

      user.value = response.data.user
      isInitialized.value = true

      return true
    } catch (error) {
      console.error('Registration failed:', error)
      throw error
    }
  }

  async function updateProfile(profileData: { username?: string }) {
    if (!user.value) throw new Error('Not authenticated')

    try {
      const response = await axios.put(`${import.meta.env.VITE_API_URL}/api/profile`, profileData)

      user.value = response.data.user
      return response.data
    } catch (error) {
      console.error('Profile update failed:', error)
      throw error
    }
  }

  async function updatePassword(passwordData: { current_password: string, new_password: string, new_password_confirmation: string }) {
    if (!user.value) throw new Error('Not authenticated')

    try {
      const response = await axios.put(`${import.meta.env.VITE_API_URL}/api/profile/password`, passwordData)

      return response.data
    } catch (error) {
      console.error('Password update failed:', error)
      throw error
    }
  }

  async function updateProfileImage(imageFile: File) {
    if (!user.value) throw new Error('Not authenticated')

    try {
      const formData = new FormData()
      formData.append('profile_image', imageFile)

      const response = await axios.post(`${import.meta.env.VITE_API_URL}/api/profile/image`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      user.value = response.data.user
      return response.data
    } catch (error) {
      console.error('Profile image update failed:', error)
      throw error
    }
  }

  async function deleteProfileImage() {
    if (!user.value) throw new Error('Not authenticated')

    try {
      const response = await axios.delete(`${import.meta.env.VITE_API_URL}/api/profile/image`)

      user.value = response.data.user
      return response.data
    } catch (error) {
      console.error('Profile image deletion failed:', error)
      throw error
    }
  }

  // Reset auth state (useful for testing or manual logout)
  function reset() {
    user.value = null
    isInitialized.value = false
    isLoading.value = false
  }

  return {
    user,
    isAuthenticated,
    isInitialized,
    isLoading,
    login,
    logout,
    fetchUser,
    register,
    updateProfile,
    updatePassword,
    updateProfileImage,
    deleteProfileImage,
    reset
  }
})
